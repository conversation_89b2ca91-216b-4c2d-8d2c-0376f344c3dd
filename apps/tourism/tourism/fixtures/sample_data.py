"""
Sample data for Tourism App
Run this script to populate the database with sample tour packages
"""
import frappe
frappe.qb.


def create_itinerary(title, start_date, end_date, duration_days):
    """Create an itinerary"""
    try:
        itinerary = frappe.get_doc({
            "doctype": "Itenarary",
            "title": title,
            "start_date": start_date,
            "end_date": end_date
        })

        itinerary.insert(ignore_permissions=True)
        frappe.db.commit()

        return itinerary.name
    except Exception as e:
        print(f"Error creating itinerary '{title}': {str(e)}")
        return None


def create_sample_packages():
    """Create sample tour packages"""

    from datetime import datetime, timedelta

    # Base date for itineraries
    base_date = datetime(2025, 11, 1)

    packages = [
        {
            "package_name": "Serengeti Safari Adventure",
            "description": """
                <p>Experience the ultimate safari adventure in the world-famous Serengeti National Park. 
                Witness the Great Migration, spot the Big Five, and immerse yourself in the breathtaking 
                landscapes of Tanzania's most iconic wildlife reserve.</p>
                
                <h3>Highlights:</h3>
                <ul>
                    <li>Game drives in Serengeti National Park</li>
                    <li>Witness the Great Migration (seasonal)</li>
                    <li>Big Five wildlife viewing</li>
                    <li>Professional safari guide</li>
                    <li>Luxury tented camp accommodation</li>
                </ul>
            """,
            "duration_days": 5,
            "price": 2500.00
        },
        {
            "package_name": "Mount Kilimanjaro Climb - Machame Route",
            "description": """
                <p>Conquer Africa's highest peak via the scenic Machame Route, also known as the 
                "Whiskey Route". This challenging but rewarding trek takes you through diverse 
                ecosystems from rainforest to alpine desert.</p>
                
                <h3>Highlights:</h3>
                <ul>
                    <li>7-day Machame Route trek</li>
                    <li>Professional mountain guides and porters</li>
                    <li>All camping equipment provided</li>
                    <li>Three meals per day on the mountain</li>
                    <li>Summit certificate upon completion</li>
                </ul>
            """,
            "duration_days": 7,
            "price": 1800.00
        },
        {
            "package_name": "Zanzibar Beach Paradise",
            "description": """
                <p>Relax and unwind on the pristine beaches of Zanzibar Island. Explore the historic 
                Stone Town, enjoy water sports, and experience the rich Swahili culture of this 
                tropical paradise.</p>
                
                <h3>Highlights:</h3>
                <ul>
                    <li>Beachfront resort accommodation</li>
                    <li>Stone Town cultural tour</li>
                    <li>Spice plantation visit</li>
                    <li>Snorkeling and diving opportunities</li>
                    <li>Sunset dhow cruise</li>
                </ul>
            """,
            "duration_days": 4,
            "price": 1200.00
        },
        {
            "package_name": "Ngorongoro Crater Explorer",
            "description": """
                <p>Discover the natural wonder of Ngorongoro Crater, the world's largest inactive 
                volcanic caldera. This UNESCO World Heritage Site offers unparalleled wildlife 
                viewing in a stunning setting.</p>
                
                <h3>Highlights:</h3>
                <ul>
                    <li>Full-day crater floor game drive</li>
                    <li>Dense wildlife population viewing</li>
                    <li>Visit to Maasai village</li>
                    <li>Crater rim lodge accommodation</li>
                    <li>Professional guide and 4x4 vehicle</li>
                </ul>
            """,
            "duration_days": 3,
            "price": 1500.00
        },
        {
            "package_name": "Northern Circuit Grand Safari",
            "description": """
                <p>The ultimate Tanzania safari experience covering all major northern parks. 
                From Tarangire's elephants to Serengeti's plains and Ngorongoro's crater, 
                this comprehensive tour showcases the best of Tanzania's wildlife.</p>
                
                <h3>Highlights:</h3>
                <ul>
                    <li>Tarangire National Park</li>
                    <li>Lake Manyara National Park</li>
                    <li>Serengeti National Park</li>
                    <li>Ngorongoro Crater</li>
                    <li>Luxury lodge accommodation</li>
                    <li>All meals and park fees included</li>
                </ul>
            """,
            "duration_days": 10,
            "price": 4500.00
        },
        {
            "package_name": "Lake Manyara & Tarangire Safari",
            "description": """
                <p>A perfect short safari combining two of Tanzania's most beautiful parks. 
                See tree-climbing lions in Lake Manyara and massive elephant herds in Tarangire, 
                all set against stunning landscapes.</p>
                
                <h3>Highlights:</h3>
                <ul>
                    <li>Lake Manyara tree-climbing lions</li>
                    <li>Tarangire elephant herds</li>
                    <li>Baobab tree landscapes</li>
                    <li>Bird watching paradise</li>
                    <li>Comfortable lodge stays</li>
                </ul>
            """,
            "duration_days": 4,
            "price": 1400.00
        }
    ]
    
    created_packages = []
    
    for pkg_data in packages:
        try:
            # Check if package already exists
            existing = frappe.db.exists("Tour Package", {"package_name": pkg_data["package_name"]})

            if existing:
                print(f"Package '{pkg_data['package_name']}' already exists. Skipping...")
                continue

            # Create itinerary for this package
            duration = pkg_data["duration_days"]
            start_date = base_date
            end_date = base_date + timedelta(days=duration - 1)

            itinerary_name = create_itinerary(
                title=f"{pkg_data['package_name']} Itinerary",
                start_date=start_date.strftime("%Y-%m-%d"),
                end_date=end_date.strftime("%Y-%m-%d"),
                duration_days=duration
            )

            if not itinerary_name:
                print(f"Failed to create itinerary for '{pkg_data['package_name']}'. Skipping package...")
                continue

            # Add itinerary to package data
            pkg_data["itinerary"] = itinerary_name

            # Create new package
            package = frappe.get_doc({
                "doctype": "Tour Package",
                **pkg_data
            })

            package.insert(ignore_permissions=True)
            created_packages.append(package.name)

            print(f"Created package: {package.package_name} ({package.name})")

        except Exception as e:
            print(f"Error creating package '{pkg_data['package_name']}': {str(e)}")
            frappe.log_error(f"Error creating sample package: {str(e)}")
    
    frappe.db.commit()
    
    print(f"\nSuccessfully created {len(created_packages)} tour packages!")
    return created_packages


def create_sample_customer():
    """Create a sample customer for testing"""
    
    try:
        # Check if customer exists
        existing = frappe.db.exists("Customer", {"email_id": "<EMAIL>"})
        
        if existing:
            print("Sample customer already exists.")
            return existing
        
        customer = frappe.get_doc({
            "doctype": "Customer",
            "customer_name": "John Doe",
            "customer_type": "Individual",
            "email_id": "<EMAIL>",
            "mobile_no": "+255 123 456 789"
        })
        
        customer.insert(ignore_permissions=True)
        frappe.db.commit()
        
        print(f"Created sample customer: {customer.name}")
        return customer.name
        
    except Exception as e:
        print(f"Error creating sample customer: {str(e)}")
        frappe.log_error(f"Error creating sample customer: {str(e)}")
        return None


def setup_sample_data():
    """Main function to setup all sample data"""
    
    print("Setting up sample data for Tourism App...")
    print("=" * 50)
    
    # Create sample packages
    print("\n1. Creating sample tour packages...")
    packages = create_sample_packages()
    
    # Create sample customer
    print("\n2. Creating sample customer...")
    customer = create_sample_customer()
    
    print("\n" + "=" * 50)
    print("Sample data setup complete!")
    print(f"Created {len(packages)} tour packages")
    print(f"Customer: {customer if customer else 'Already exists'}")
    print("\nYou can now test the Tourism App with this sample data.")


if __name__ == "__main__":
    setup_sample_data()

