import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';

export function WireframeVisualization() {
  return (
    <div className="space-y-8">
      {/* Homepage Wireframe */}
      <Card>
        <CardHeader>
          <CardTitle>Homepage Wireframe</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="border-2 border-gray-300 rounded-lg overflow-hidden bg-white">
            {/* Header */}
            <div className="bg-gray-100 p-4 border-b-2 border-gray-300">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-8">
                  <div className="w-32 h-8 bg-amber-200 rounded flex items-center justify-center text-xs">Logo</div>
                  <div className="hidden md:flex gap-4">
                    {['Home', 'Shop', 'Tours', 'About'].map(item => (
                      <div key={item} className="w-16 h-6 bg-gray-200 rounded"></div>
                    ))}
                  </div>
                </div>
                <div className="flex gap-2">
                  <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                  <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                </div>
              </div>
            </div>
            
            {/* Hero Section */}
            <div className="bg-gradient-to-r from-amber-100 to-orange-100 p-8 md:p-16 text-center">
              <div className="w-48 h-8 bg-gray-300 rounded mx-auto mb-4"></div>
              <div className="w-64 h-4 bg-gray-200 rounded mx-auto mb-8"></div>
              <div className="flex gap-4 justify-center mb-6">
                <div className="w-32 h-10 bg-amber-400 rounded"></div>
                <div className="w-32 h-10 bg-gray-300 rounded"></div>
              </div>
              <div className="w-full max-w-2xl mx-auto h-12 bg-white rounded-full shadow"></div>
            </div>

            {/* Content Sections */}
            <div className="p-8 space-y-8">
              {/* Tour Cards */}
              <div>
                <div className="w-40 h-6 bg-gray-300 rounded mb-4"></div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {[1, 2, 3].map(i => (
                    <div key={i} className="border border-gray-300 rounded-lg overflow-hidden">
                      <div className="w-full h-32 bg-gray-200"></div>
                      <div className="p-4 space-y-2">
                        <div className="w-3/4 h-4 bg-gray-300 rounded"></div>
                        <div className="w-full h-3 bg-gray-200 rounded"></div>
                        <div className="w-full h-3 bg-gray-200 rounded"></div>
                        <div className="flex justify-between items-center mt-4">
                          <div className="w-16 h-6 bg-amber-200 rounded"></div>
                          <div className="w-20 h-8 bg-amber-400 rounded"></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="bg-gray-800 p-8">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-4">
                {[1, 2, 3, 4].map(i => (
                  <div key={i} className="space-y-2">
                    <div className="w-24 h-4 bg-gray-600 rounded"></div>
                    <div className="w-32 h-3 bg-gray-700 rounded"></div>
                    <div className="w-28 h-3 bg-gray-700 rounded"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Shop Page Wireframe */}
      <Card>
        <CardHeader>
          <CardTitle>Shop Page Wireframe</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="border-2 border-gray-300 rounded-lg overflow-hidden bg-white">
            <div className="p-8">
              <div className="flex justify-between items-center mb-6">
                <div className="w-48 h-8 bg-gray-300 rounded"></div>
                <div className="w-40 h-10 bg-amber-400 rounded flex items-center justify-center text-xs">+ Add Product</div>
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {[1, 2, 3, 4, 5, 6, 7, 8].map(i => (
                  <div key={i} className="border border-gray-300 rounded-lg overflow-hidden">
                    <div className="w-full h-32 bg-gray-200"></div>
                    <div className="p-3 space-y-2">
                      <div className="w-full h-3 bg-gray-300 rounded"></div>
                      <div className="flex items-center gap-1">
                        {[1, 2, 3, 4, 5].map(star => (
                          <div key={star} className="w-3 h-3 bg-amber-300 rounded-full"></div>
                        ))}
                      </div>
                      <div className="w-16 h-4 bg-amber-200 rounded"></div>
                      <div className="w-full h-8 bg-amber-400 rounded text-xs flex items-center justify-center">Add to Cart</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reviews Page Wireframe */}
      <Card>
        <CardHeader>
          <CardTitle>Reviews Page Wireframe</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="border-2 border-gray-300 rounded-lg overflow-hidden bg-white">
            <div className="p-8">
              <div className="flex justify-between items-center mb-6">
                <div className="w-48 h-8 bg-gray-300 rounded"></div>
                <div className="w-40 h-10 bg-amber-400 rounded flex items-center justify-center text-xs">Write Review</div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {[1, 2, 3].map(i => (
                  <div key={i} className="border border-gray-300 rounded-lg p-4 space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-amber-300 rounded-full"></div>
                      <div className="flex-1 space-y-2">
                        <div className="w-24 h-3 bg-gray-300 rounded"></div>
                        <div className="w-16 h-2 bg-gray-200 rounded"></div>
                      </div>
                    </div>
                    <div className="flex gap-1">
                      {[1, 2, 3, 4, 5].map(star => (
                        <div key={star} className="w-4 h-4 bg-amber-400 rounded-full"></div>
                      ))}
                    </div>
                    <div className="space-y-1">
                      <div className="w-full h-2 bg-gray-200 rounded"></div>
                      <div className="w-full h-2 bg-gray-200 rounded"></div>
                      <div className="w-3/4 h-2 bg-gray-200 rounded"></div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-16 h-6 bg-gray-100 rounded flex items-center justify-center text-xs">👍 12</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Booking Flow Wireframe */}
      <Card>
        <CardHeader>
          <CardTitle>Booking Flow Wireframe</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="border-2 border-gray-300 rounded-lg overflow-hidden bg-white">
            <div className="p-8">
              {/* Steps */}
              <div className="flex justify-between items-center mb-8">
                {['Select Tour', 'Choose Dates', 'Guest Info', 'Payment'].map((step, idx) => (
                  <div key={step} className="flex-1 flex items-center">
                    <div className={`w-10 h-10 ${idx === 0 ? 'bg-amber-400' : 'bg-gray-200'} rounded-full flex items-center justify-center text-xs`}>
                      {idx + 1}
                    </div>
                    {idx < 3 && <div className="flex-1 h-1 bg-gray-200 mx-2"></div>}
                  </div>
                ))}
              </div>

              {/* Form */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <div className="w-24 h-3 bg-gray-300 rounded mb-2"></div>
                    <div className="w-full h-10 bg-gray-100 border border-gray-300 rounded"></div>
                  </div>
                  <div>
                    <div className="w-24 h-3 bg-gray-300 rounded mb-2"></div>
                    <div className="w-full h-10 bg-gray-100 border border-gray-300 rounded"></div>
                  </div>
                  <div>
                    <div className="w-24 h-3 bg-gray-300 rounded mb-2"></div>
                    <div className="w-full h-10 bg-gray-100 border border-gray-300 rounded"></div>
                  </div>
                </div>
                
                <div className="border border-gray-300 rounded-lg p-4">
                  <div className="w-32 h-4 bg-gray-300 rounded mb-4"></div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <div className="w-24 h-3 bg-gray-200 rounded"></div>
                      <div className="w-16 h-3 bg-gray-300 rounded"></div>
                    </div>
                    <div className="flex justify-between">
                      <div className="w-24 h-3 bg-gray-200 rounded"></div>
                      <div className="w-16 h-3 bg-gray-300 rounded"></div>
                    </div>
                    <div className="border-t border-gray-300 my-2"></div>
                    <div className="flex justify-between">
                      <div className="w-16 h-4 bg-gray-300 rounded"></div>
                      <div className="w-20 h-4 bg-amber-300 rounded"></div>
                    </div>
                  </div>
                  <div className="w-full h-10 bg-amber-400 rounded mt-4"></div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
