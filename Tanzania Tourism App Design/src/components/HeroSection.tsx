import { Button } from './ui/button';
import { Search } from 'lucide-react';
import { ImageWithFallback } from './figma/ImageWithFallback';

export function HeroSection() {
  return (
    <section id="home" className="relative h-[600px] lg:h-[700px] flex items-center justify-center">
      <div className="absolute inset-0">
        <ImageWithFallback
          src="https://images.unsplash.com/photo-1667550507974-cc647990b75a?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxUYW56YW5pYSUyMHNhZmFyaSUyMHdpbGRsaWZlfGVufDF8fHx8MTc2MDI4MDYyOHww&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral"
          alt="Tanzania Safari"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black/40"></div>
      </div>
      
      <div className="relative z-10 container mx-auto px-4 text-center text-white">
        <h1 className="text-5xl lg:text-7xl mb-6">Discover Tanzania's Wonders</h1>
        <p className="text-xl lg:text-2xl mb-8 max-w-2xl mx-auto">
          Experience the magic of African wildlife, stunning landscapes, and rich cultural heritage
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
          <Button size="lg" className="bg-amber-600 hover:bg-amber-700 text-white">
            Explore Tours
          </Button>
          <Button size="lg" variant="outline" className="bg-white/10 backdrop-blur-sm text-white border-white hover:bg-white/20">
            Learn More
          </Button>
        </div>

        {/* Search Bar */}
        <div className="max-w-3xl mx-auto bg-white rounded-full p-2 flex items-center gap-2">
          <Search className="h-5 w-5 text-gray-400 ml-4" />
          <input
            type="text"
            placeholder="Search destinations, activities, or packages..."
            className="flex-1 px-4 py-3 outline-none text-gray-800"
          />
          <Button className="bg-amber-600 hover:bg-amber-700 rounded-full">
            Search
          </Button>
        </div>
      </div>
    </section>
  );
}
