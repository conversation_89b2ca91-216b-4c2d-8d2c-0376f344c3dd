import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, <PERSON>Title } from './ui/card';
import { <PERSON>Down, <PERSON>R<PERSON>, Check } from 'lucide-react';

export function SystemWorkflow() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl mb-4">How SevenSerenity Safaris Works</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            A complete walkthrough of the tourism app workflow
          </p>
        </div>

        <div className="max-w-5xl mx-auto">
          {/* Main Flow */}
          <div className="space-y-6">
            {/* Step 1 */}
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 h-12 w-12 bg-amber-600 rounded-full flex items-center justify-center text-white">
                1
              </div>
              <Card className="flex-1">
                <CardHeader>
                  <CardTitle>User Arrives at Home Page</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <p className="text-gray-600">The landing page features:</p>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Hero section with stunning Tanzania safari imagery</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Search bar to find destinations, activities, or packages</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Featured tour packages preview</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Luxury accommodations showcase</span>
                      </li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="flex justify-center">
              <ArrowDown className="h-8 w-8 text-amber-600" />
            </div>

            {/* Step 2 */}
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 h-12 w-12 bg-amber-600 rounded-full flex items-center justify-center text-white">
                2
              </div>
              <Card className="flex-1">
                <CardHeader>
                  <CardTitle>Explore Through Navigation</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <p className="text-gray-600">Header navigation provides access to:</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                      <div className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span><strong>Shop:</strong> Browse and purchase souvenirs</span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span><strong>Tour Package:</strong> View safari options</span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span><strong>Accommodation:</strong> Check lodges & camps</span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span><strong>Activities:</strong> Safari experiences</span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span><strong>Heritage:</strong> Cultural information</span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span><strong>Geography:</strong> Destinations & parks</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="flex justify-center">
              <ArrowDown className="h-8 w-8 text-amber-600" />
            </div>

            {/* Step 3 - Split Flow */}
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 h-12 w-12 bg-amber-600 rounded-full flex items-center justify-center text-white">
                3
              </div>
              <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="border-2 border-amber-200">
                  <CardHeader>
                    <CardTitle className="text-lg">Path A: Shopping</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Browse authentic souvenirs</span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>View product ratings</span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Add items to cart</span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Vendors can add new products</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-2 border-amber-200">
                  <CardHeader>
                    <CardTitle className="text-lg">Path B: Tour Booking</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Select tour package</span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Choose accommodation</span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Go to booking page</span>
                      </div>
                      <div className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Fill reservation form</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            <div className="flex justify-center">
              <ArrowDown className="h-8 w-8 text-amber-600" />
            </div>

            {/* Step 4 */}
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 h-12 w-12 bg-amber-600 rounded-full flex items-center justify-center text-white">
                4
              </div>
              <Card className="flex-1">
                <CardHeader>
                  <CardTitle>Interactive Features</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="space-y-2">
                      <h5 className="font-semibold text-amber-600">Reviews System</h5>
                      <ul className="space-y-1">
                        <li className="flex items-start gap-2">
                          <Check className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                          <span>Write reviews</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <Check className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                          <span>Rate with stars (1-5)</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <Check className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                          <span>Like/unlike reviews</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <Check className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                          <span>Dynamic like counter</span>
                        </li>
                      </ul>
                    </div>

                    <div className="space-y-2">
                      <h5 className="font-semibold text-amber-600">Shop Features</h5>
                      <ul className="space-y-1">
                        <li className="flex items-start gap-2">
                          <Check className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                          <span>Add new products</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <Check className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                          <span>Upload product images</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <Check className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                          <span>Set pricing</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <Check className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                          <span>Shopping cart</span>
                        </li>
                      </ul>
                    </div>

                    <div className="space-y-2">
                      <h5 className="font-semibold text-amber-600">Support Center</h5>
                      <ul className="space-y-1">
                        <li className="flex items-start gap-2">
                          <Check className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                          <span>FAQ accordion</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <Check className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                          <span>Contact form</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <Check className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                          <span>Live chat option</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <Check className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                          <span>Phone & email</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="flex justify-center">
              <ArrowDown className="h-8 w-8 text-amber-600" />
            </div>

            {/* Step 5 */}
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 h-12 w-12 bg-green-600 rounded-full flex items-center justify-center text-white">
                5
              </div>
              <Card className="flex-1 border-2 border-green-200">
                <CardHeader>
                  <CardTitle>Post-Experience</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <p className="text-gray-600">After the safari experience:</p>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Return to reviews page to share experience</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Submit star rating and detailed review</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Read and like other travelers' reviews</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Access support for any follow-up questions</span>
                      </li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Technical Flow */}
          <div className="mt-12 p-6 bg-gray-50 rounded-lg border-2 border-gray-200">
            <h3 className="text-xl font-semibold mb-6 text-center">Technical Architecture</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="h-16 w-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <span className="text-2xl">⚛️</span>
                </div>
                <h5 className="font-semibold mb-1">React</h5>
                <p className="text-xs text-gray-600">Component-based UI with TypeScript</p>
              </div>

              <div className="text-center">
                <div className="h-16 w-16 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <span className="text-2xl">🎨</span>
                </div>
                <h5 className="font-semibold mb-1">Tailwind CSS</h5>
                <p className="text-xs text-gray-600">Utility-first styling system</p>
              </div>

              <div className="text-center">
                <div className="h-16 w-16 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <span className="text-2xl">🔧</span>
                </div>
                <h5 className="font-semibold mb-1">ShadCN UI</h5>
                <p className="text-xs text-gray-600">Pre-built accessible components</p>
              </div>

              <div className="text-center">
                <div className="h-16 w-16 bg-amber-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <span className="text-2xl">🔄</span>
                </div>
                <h5 className="font-semibold mb-1">State Management</h5>
                <p className="text-xs text-gray-600">React hooks for local state</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
