import { <PERSON>, CardContent, CardHeader } from './ui/card';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Star, Quote } from 'lucide-react';

const reviews = [
  {
    id: 1,
    name: '<PERSON>',
    location: 'United States',
    avatar: 'SJ',
    rating: 5,
    date: 'September 2024',
    comment: 'An absolutely incredible experience! The guides were knowledgeable, accommodations were luxurious, and seeing the wildlife up close was a dream come true. Highly recommend SevenSerenity Safaris!',
  },
  {
    id: 2,
    name: '<PERSON>',
    location: 'Italy',
    avatar: 'MR',
    rating: 5,
    date: 'August 2024',
    comment: 'Our Kilimanjaro climb was expertly organized. The team made sure we were safe and comfortable throughout. The views from the summit were breathtaking!',
  },
  {
    id: 3,
    name: '<PERSON><PERSON>',
    location: 'Japan',
    avatar: 'YT',
    rating: 5,
    date: 'July 2024',
    comment: 'The cultural tour combined with wildlife viewing was perfect. Learning about Maasai traditions and seeing elephants in their natural habitat was unforgettable.',
  },
  {
    id: 4,
    name: '<PERSON>',
    location: 'United Kingdom',
    avatar: 'EW',
    rating: 5,
    date: 'October 2024',
    comment: 'Best safari experience ever! The attention to detail and personalized service made our honeymoon truly special. Thank you SevenSerenity!',
  },
];

export function Reviews() {
  return (
    <section id="reviews" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl mb-4">What Our Guests Say</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Read reviews from travelers who experienced the magic of Tanzania with us
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {reviews.map((review) => (
            <Card key={review.id} className="hover:shadow-xl transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <Avatar>
                      <AvatarFallback className="bg-amber-600 text-white">
                        {review.avatar}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{review.name}</p>
                      <p className="text-sm text-gray-500">{review.location}</p>
                    </div>
                  </div>
                  <Quote className="h-6 w-6 text-amber-600/20" />
                </div>
                <div className="flex items-center gap-1 mb-2">
                  {[...Array(review.rating)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-amber-500 text-amber-500" />
                  ))}
                </div>
                <p className="text-xs text-gray-500">{review.date}</p>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-700 leading-relaxed">{review.comment}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
