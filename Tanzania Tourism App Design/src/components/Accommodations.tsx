import { Card, CardContent, CardDescription, <PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>er, CardTitle } from './ui/card';
import { But<PERSON> } from './ui/button';
import { Star, Wifi, Coffee, MapPin } from 'lucide-react';
import { ImageWithFallback } from './figma/ImageWithFallback';

const accommodations = [
  {
    id: 1,
    name: 'Serengeti Luxury Lodge',
    description: 'Experience luxury in the heart of the wilderness',
    image: 'https://images.unsplash.com/photo-1650018001659-b4e5e7c0ea5d?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxsdXh1cnklMjBzYWZhcmklMjBsb2RnZXxlbnwxfHx8fDE3NjAyODA2Mjl8MA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
    location: 'Serengeti National Park',
    rating: 4.9,
    price: '$450/night',
    amenities: ['WiFi', 'Restaurant', 'Pool', 'Spa'],
  },
  {
    id: 2,
    name: 'Ngorongoro Crater Camp',
    description: 'Glamping experience with stunning crater views',
    image: 'https://images.unsplash.com/photo-1650018001659-b4e5e7c0ea5d?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxsdXh1cnklMjBzYWZhcmklMjBsb2RnZXxlbnwxfHx8fDE3NjAyODA2Mjl8MA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
    location: 'Ngorongoro Conservation Area',
    rating: 4.8,
    price: '$380/night',
    amenities: ['WiFi', 'Restaurant', 'Bar', 'Game Drives'],
  },
  {
    id: 3,
    name: 'Zanzibar Beach Resort',
    description: 'Relax on pristine white sand beaches',
    image: 'https://images.unsplash.com/photo-1650018001659-b4e5e7c0ea5d?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxsdXh1cnklMjBzYWZhcmklMjBsb2RnZXxlbnwxfHx8fDE3NjAyODA2Mjl8MA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
    location: 'Zanzibar',
    rating: 4.7,
    price: '$320/night',
    amenities: ['WiFi', 'Beach Access', 'Water Sports', 'Spa'],
  },
  {
    id: 4,
    name: 'Tarangire Safari Lodge',
    description: 'Intimate lodge overlooking the Tarangire River',
    image: 'https://images.unsplash.com/photo-1650018001659-b4e5e7c0ea5d?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxsdXh1cnklMjBzYWZhcmklMjBsb2RnZXxlbnwxfHx8fDE3NjAyODA2Mjl8MA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
    location: 'Tarangire National Park',
    rating: 4.6,
    price: '$280/night',
    amenities: ['WiFi', 'Restaurant', 'Pool', 'Safari Tours'],
  },
];

export function Accommodations() {
  return (
    <section id="accommodation" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl mb-4">Luxury Accommodations</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Stay in comfort while experiencing the wild beauty of Tanzania
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {accommodations.map((accommodation) => (
            <Card key={accommodation.id} className="overflow-hidden hover:shadow-xl transition-shadow">
              <div className="relative h-48">
                <ImageWithFallback
                  src={accommodation.image}
                  alt={accommodation.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <CardHeader>
                <div className="flex justify-between items-start mb-2">
                  <CardTitle className="text-lg">{accommodation.name}</CardTitle>
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 fill-amber-500 text-amber-500" />
                    <span className="text-sm">{accommodation.rating}</span>
                  </div>
                </div>
                <CardDescription className="flex items-start gap-1">
                  <MapPin className="h-3 w-3 mt-1 flex-shrink-0" />
                  <span className="text-xs">{accommodation.location}</span>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-3">{accommodation.description}</p>
                <div className="flex flex-wrap gap-2 mb-3">
                  {accommodation.amenities.slice(0, 2).map((amenity, index) => (
                    <span key={index} className="text-xs bg-gray-100 px-2 py-1 rounded">
                      {amenity}
                    </span>
                  ))}
                </div>
                <p className="text-xl text-amber-600">{accommodation.price}</p>
              </CardContent>
              <CardFooter>
                <Button className="w-full bg-amber-600 hover:bg-amber-700">
                  View Details
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
