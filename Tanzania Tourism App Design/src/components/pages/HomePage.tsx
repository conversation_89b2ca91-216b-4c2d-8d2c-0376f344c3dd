import { HeroSection } from '../HeroSection';
import { TourPackages } from '../TourPackages';
import { Accommodations } from '../Accommodations';

interface HomePageProps {
  onNavigate: (page: string) => void;
}

export function HomePage({ onNavigate }: HomePageProps) {
  return (
    <div>
      <HeroSection onNavigate={onNavigate} />
      <TourPackages onNavigate={onNavigate} />
      <Accommodations onNavigate={onNavigate} />
    </div>
  );
}
