import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '../ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '../ui/tabs';
import { MapPin, Mountain, Waves, TreePine, Sun } from 'lucide-react';
import { ImageWithFallback } from '../figma/ImageWithFallback';

export function GeographyPage() {
  return (
    <section className="py-20 bg-gray-50 min-h-screen">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl mb-4">Geography & Tours</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover the diverse landscapes and regions of Tanzania
          </p>
        </div>

        <Tabs defaultValue="regions" className="w-full">
          <TabsList className="grid w-full max-w-md mx-auto grid-cols-2 mb-8">
            <TabsTrigger value="regions">Regions</TabsTrigger>
            <TabsTrigger value="climate">Climate</TabsTrigger>
          </TabsList>

          <TabsContent value="regions">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="hover:shadow-xl transition-shadow">
                <div className="relative h-48">
                  <ImageWithFallback
                    src="https://images.unsplash.com/photo-1464388334095-4e05c18297cd?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxTZXJlbmdldGklMjBsYW5kc2NhcGV8ZW58MXx8fHwxNzYwMjgwNjMwfDA&ixlib=rb-4.1.0&q=80&w=1080"
                    alt="Northern Safari Circuit"
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardHeader>
                  <div className="flex items-center gap-2 mb-2">
                    <MapPin className="h-5 w-5 text-amber-600" />
                    <CardTitle>Northern Safari Circuit</CardTitle>
                  </div>
                  <CardDescription>
                    Serengeti, Ngorongoro, Tarangire, Lake Manyara
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">
                    The most popular safari destination featuring incredible wildlife and the Great Migration.
                  </p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-xl transition-shadow">
                <div className="relative h-48">
                  <ImageWithFallback
                    src="https://images.unsplash.com/photo-1489392191049-fc10c97e64b6?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxNb3VudCUyMEtpbGltYW5qYXJvfGVufDF8fHx8MTc2MDI4MDYzMHww&ixlib=rb-4.1.0&q=80&w=1080"
                    alt="Mount Kilimanjaro Region"
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardHeader>
                  <div className="flex items-center gap-2 mb-2">
                    <Mountain className="h-5 w-5 text-amber-600" />
                    <CardTitle>Mount Kilimanjaro Region</CardTitle>
                  </div>
                  <CardDescription>
                    Africa's highest peak at 5,895 meters
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">
                    A dormant volcano and one of the world's most accessible high summits.
                  </p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-xl transition-shadow">
                <div className="relative h-48">
                  <ImageWithFallback
                    src="https://images.unsplash.com/photo-1667550507974-cc647990b75a?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxUYW56YW5pYSUyMHNhZmFyaSUyMHdpbGRsaWZlfGVufDF8fHx8MTc2MDI4MDYyOHww&ixlib=rb-4.1.0&q=80&w=1080"
                    alt="Zanzibar Archipelago"
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardHeader>
                  <div className="flex items-center gap-2 mb-2">
                    <Waves className="h-5 w-5 text-amber-600" />
                    <CardTitle>Zanzibar Archipelago</CardTitle>
                  </div>
                  <CardDescription>
                    Pristine beaches and historic Stone Town
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">
                    Tropical paradise with crystal-clear waters, coral reefs, and rich cultural heritage.
                  </p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-xl transition-shadow">
                <div className="relative h-48">
                  <ImageWithFallback
                    src="https://images.unsplash.com/photo-1464388334095-4e05c18297cd?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxTZXJlbmdldGklMjBsYW5kc2NhcGV8ZW58MXx8fHwxNzYwMjgwNjMwfDA&ixlib=rb-4.1.0&q=80&w=1080"
                    alt="Southern Tanzania"
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardHeader>
                  <div className="flex items-center gap-2 mb-2">
                    <TreePine className="h-5 w-5 text-amber-600" />
                    <CardTitle>Southern Tanzania</CardTitle>
                  </div>
                  <CardDescription>
                    Selous, Ruaha, and remote wilderness
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">
                    Off-the-beaten-path destinations offering exclusive wildlife experiences.
                  </p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-xl transition-shadow">
                <div className="relative h-48">
                  <ImageWithFallback
                    src="https://images.unsplash.com/photo-1489392191049-fc10c97e64b6?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxNb3VudCUyMEtpbGltYW5qYXJvfGVufDF8fHx8MTc2MDI4MDYzMHww&ixlib=rb-4.1.0&q=80&w=1080"
                    alt="Western Tanzania"
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardHeader>
                  <div className="flex items-center gap-2 mb-2">
                    <MapPin className="h-5 w-5 text-amber-600" />
                    <CardTitle>Western Tanzania</CardTitle>
                  </div>
                  <CardDescription>
                    Lake Tanganyika and Mahale Mountains
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">
                    Home to chimpanzee trekking and the world's second deepest lake.
                  </p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-xl transition-shadow">
                <div className="relative h-48">
                  <ImageWithFallback
                    src="https://images.unsplash.com/photo-1667550507974-cc647990b75a?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxUYW56YW5pYSUyMHNhZmFyaSUyMHdpbGRsaWZlfGVufDF8fHx8MTc2MDI4MDYyOHww&ixlib=rb-4.1.0&q=80&w=1080"
                    alt="Central Tanzania"
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardHeader>
                  <div className="flex items-center gap-2 mb-2">
                    <Sun className="h-5 w-5 text-amber-600" />
                    <CardTitle>Central Tanzania</CardTitle>
                  </div>
                  <CardDescription>
                    Dodoma capital and archaeological sites
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">
                    Less touristic region with unique rock art and cultural experiences.
                  </p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="climate">
            <div className="max-w-4xl mx-auto">
              <Card>
                <CardHeader>
                  <CardTitle>Climate Information</CardTitle>
                  <CardDescription>Understanding Tanzania's weather patterns</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h4 className="mb-2">Dry Season (June - October)</h4>
                    <p className="text-sm text-gray-600">
                      The best time for wildlife viewing with minimal rainfall and comfortable temperatures. 
                      Perfect for safari adventures and climbing Mount Kilimanjaro.
                    </p>
                  </div>
                  <div>
                    <h4 className="mb-2">Short Rains (November - December)</h4>
                    <p className="text-sm text-gray-600">
                      Brief afternoon showers bring lush green landscapes. Great for photography and 
                      fewer tourists mean more intimate wildlife encounters.
                    </p>
                  </div>
                  <div>
                    <h4 className="mb-2">Long Rains (March - May)</h4>
                    <p className="text-sm text-gray-600">
                      Heavy rainfall season. Some lodges close, but you'll find the best rates and 
                      spectacular bird watching opportunities.
                    </p>
                  </div>
                  <div>
                    <h4 className="mb-2">Hot Season (January - February)</h4>
                    <p className="text-sm text-gray-600">
                      Warm and dry period, ideal for calving season in the Serengeti. Excellent time 
                      for beach holidays in Zanzibar.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </section>
  );
}
