import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, CardTitle } from '../ui/card';
import { <PERSON><PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { Camera, Compass, Mountain, Palmtree, Users, Waves } from 'lucide-react';

const activities = [
  {
    id: 1,
    title: 'Game Drives',
    description: 'Experience close encounters with Africa\'s Big Five in their natural habitat',
    icon: Compass,
    duration: 'Half Day / Full Day',
    difficulty: 'Easy',
    color: 'bg-blue-100 text-blue-600',
  },
  {
    id: 2,
    title: 'Mountain Trekking',
    description: 'Challenge yourself with guided treks on Mount Kilimanjaro and Mount Meru',
    icon: Mountain,
    duration: '5-8 Days',
    difficulty: 'Challenging',
    color: 'bg-green-100 text-green-600',
  },
  {
    id: 3,
    title: 'Beach & Water Sports',
    description: 'Relax on Zanzibar\'s pristine beaches or try snorkeling and diving',
    icon: Waves,
    duration: 'Flexible',
    difficulty: 'Easy to Moderate',
    color: 'bg-cyan-100 text-cyan-600',
  },
  {
    id: 4,
    title: 'Cultural Tours',
    description: 'Visit traditional Maasai villages and learn about local customs',
    icon: Users,
    duration: 'Half Day',
    difficulty: 'Easy',
    color: 'bg-purple-100 text-purple-600',
  },
  {
    id: 5,
    title: 'Photography Safaris',
    description: 'Capture stunning wildlife and landscapes with professional guidance',
    icon: Camera,
    duration: 'Customizable',
    difficulty: 'Easy to Moderate',
    color: 'bg-amber-100 text-amber-600',
  },
  {
    id: 6,
    title: 'Island Hopping',
    description: 'Explore the exotic islands of Zanzibar archipelago',
    icon: Palmtree,
    duration: '2-3 Days',
    difficulty: 'Easy',
    color: 'bg-orange-100 text-orange-600',
  },
];

export function ActivitiesPage() {
  return (
    <section className="py-20 bg-gray-50 min-h-screen">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl mb-4">Safari Activities</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover the wide range of exciting activities and experiences we offer
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {activities.map((activity) => {
            const Icon = activity.icon;
            return (
              <Card key={activity.id} className="hover:shadow-xl transition-shadow">
                <CardHeader>
                  <div className={`h-16 w-16 ${activity.color} rounded-full flex items-center justify-center mb-4`}>
                    <Icon className="h-8 w-8" />
                  </div>
                  <CardTitle>{activity.title}</CardTitle>
                  <CardDescription>{activity.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Duration:</span>
                      <Badge variant="outline">{activity.duration}</Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Difficulty:</span>
                      <Badge variant="outline">{activity.difficulty}</Badge>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button className="w-full bg-amber-600 hover:bg-amber-700">
                    Learn More
                  </Button>
                </CardFooter>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
}
