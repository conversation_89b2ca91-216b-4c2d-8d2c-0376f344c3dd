import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from '../ui/card';
import { Avatar, AvatarFallback } from '../ui/avatar';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { Label } from '../ui/label';
import { Star, Quote, ThumbsUp, Plus } from 'lucide-react';

interface Review {
  id: number;
  name: string;
  location: string;
  avatar: string;
  rating: number;
  date: string;
  comment: string;
  likes: number;
}

const initialReviews: Review[] = [
  {
    id: 1,
    name: '<PERSON>',
    location: 'United States',
    avatar: 'SJ',
    rating: 5,
    date: 'September 2024',
    comment: 'An absolutely incredible experience! The guides were knowledgeable, accommodations were luxurious, and seeing the wildlife up close was a dream come true. Highly recommend SevenSerenity Safaris!',
    likes: 12,
  },
  {
    id: 2,
    name: '<PERSON>',
    location: 'Italy',
    avatar: 'MR',
    rating: 5,
    date: 'August 2024',
    comment: 'Our Kilimanjaro climb was expertly organized. The team made sure we were safe and comfortable throughout. The views from the summit were breathtaking!',
    likes: 8,
  },
  {
    id: 3,
    name: 'Yuki Tanaka',
    location: 'Japan',
    avatar: 'YT',
    rating: 5,
    date: 'July 2024',
    comment: 'The cultural tour combined with wildlife viewing was perfect. Learning about Maasai traditions and seeing elephants in their natural habitat was unforgettable.',
    likes: 15,
  },
];

export function ReviewsPage() {
  const [reviews, setReviews] = useState<Review[]>(initialReviews);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [likedReviews, setLikedReviews] = useState<Set<number>>(new Set());
  const [newReview, setNewReview] = useState({
    name: '',
    location: '',
    rating: 5,
    comment: '',
  });

  const handleAddReview = () => {
    if (newReview.name && newReview.location && newReview.comment) {
      const review: Review = {
        id: Date.now(),
        name: newReview.name,
        location: newReview.location,
        avatar: newReview.name.split(' ').map(n => n[0]).join('').toUpperCase(),
        rating: newReview.rating,
        date: new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' }),
        comment: newReview.comment,
        likes: 0,
      };
      setReviews([review, ...reviews]);
      setNewReview({ name: '', location: '', rating: 5, comment: '' });
      setIsAddDialogOpen(false);
    }
  };

  const handleLike = (reviewId: number) => {
    if (likedReviews.has(reviewId)) {
      // Unlike
      setLikedReviews(prev => {
        const newSet = new Set(prev);
        newSet.delete(reviewId);
        return newSet;
      });
      setReviews(reviews.map(r => 
        r.id === reviewId ? { ...r, likes: r.likes - 1 } : r
      ));
    } else {
      // Like
      setLikedReviews(prev => new Set(prev).add(reviewId));
      setReviews(reviews.map(r => 
        r.id === reviewId ? { ...r, likes: r.likes + 1 } : r
      ));
    }
  };

  return (
    <section className="py-20 bg-white min-h-screen">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl mb-4">Guest Reviews & Comments</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Read reviews from travelers who experienced the magic of Tanzania with us
          </p>
        </div>

        <div className="flex justify-end mb-6">
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-amber-600 hover:bg-amber-700">
                <Plus className="h-4 w-4 mr-2" />
                Write a Review
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Write Your Review</DialogTitle>
                <DialogDescription>
                  Share your experience with SevenSerenity Safaris
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="reviewer-name">Your Name</Label>
                  <Input
                    id="reviewer-name"
                    value={newReview.name}
                    onChange={(e) => setNewReview({ ...newReview, name: e.target.value })}
                    placeholder="Enter your name"
                  />
                </div>
                <div>
                  <Label htmlFor="reviewer-location">Location</Label>
                  <Input
                    id="reviewer-location"
                    value={newReview.location}
                    onChange={(e) => setNewReview({ ...newReview, location: e.target.value })}
                    placeholder="Your country/city"
                  />
                </div>
                <div>
                  <Label htmlFor="reviewer-rating">Rating</Label>
                  <div className="flex gap-2 mt-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        type="button"
                        onClick={() => setNewReview({ ...newReview, rating: star })}
                      >
                        <Star
                          className={`h-6 w-6 cursor-pointer transition-colors ${
                            star <= newReview.rating
                              ? 'fill-amber-500 text-amber-500'
                              : 'text-gray-300'
                          }`}
                        />
                      </button>
                    ))}
                  </div>
                </div>
                <div>
                  <Label htmlFor="reviewer-comment">Your Review</Label>
                  <Textarea
                    id="reviewer-comment"
                    value={newReview.comment}
                    onChange={(e) => setNewReview({ ...newReview, comment: e.target.value })}
                    placeholder="Share your experience..."
                    rows={4}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddReview} className="bg-amber-600 hover:bg-amber-700">
                  Submit Review
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {reviews.map((review) => (
            <Card key={review.id} className="hover:shadow-xl transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <Avatar>
                      <AvatarFallback className="bg-amber-600 text-white">
                        {review.avatar}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{review.name}</p>
                      <p className="text-sm text-gray-500">{review.location}</p>
                    </div>
                  </div>
                  <Quote className="h-6 w-6 text-amber-600/20" />
                </div>
                <div className="flex items-center gap-1 mb-2">
                  {[...Array(review.rating)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-amber-500 text-amber-500" />
                  ))}
                </div>
                <p className="text-xs text-gray-500">{review.date}</p>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-700 leading-relaxed mb-4">{review.comment}</p>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleLike(review.id)}
                    className={likedReviews.has(review.id) ? 'text-amber-600' : ''}
                  >
                    <ThumbsUp className={`h-4 w-4 mr-1 ${likedReviews.has(review.id) ? 'fill-amber-600' : ''}`} />
                    {review.likes}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
