import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '../ui/tabs';
import { 
  Home, 
  ShoppingCart, 
  Package, 
  Building, 
  Activity, 
  Landmark, 
  Map, 
  Info, 
  Calendar,
  Star,
  HelpCircle,
  ArrowRight,
  Users,
  MessageSquare
} from 'lucide-react';

export function AppDocumentation() {
  return (
    <section className="py-20 bg-gray-50 min-h-screen">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl mb-4">App Architecture & User Flow</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Complete documentation of SevenSerenity Safaris Tourism App
          </p>
        </div>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="navigation">Navigation</TabsTrigger>
            <TabsTrigger value="features">Features</TabsTrigger>
            <TabsTrigger value="wireframes">Wireframes</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Application Overview</CardTitle>
                <CardDescription>
                  SevenSerenity Safaris is a comprehensive tourism platform for Tanzania
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="font-semibold">Core Features:</h4>
                    <ul className="space-y-1 text-sm text-gray-600">
                      <li>• Interactive tour package browsing</li>
                      <li>• Luxury accommodation listings</li>
                      <li>• E-commerce shop for souvenirs</li>
                      <li>• User reviews and ratings system</li>
                      <li>• Online booking system</li>
                      <li>• Support and help center</li>
                      <li>• Geography and heritage information</li>
                    </ul>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-semibold">Technical Stack:</h4>
                    <ul className="space-y-1 text-sm text-gray-600">
                      <li>• React with TypeScript</li>
                      <li>• Tailwind CSS for styling</li>
                      <li>• ShadCN UI components</li>
                      <li>• State management with React hooks</li>
                      <li>• Client-side routing</li>
                      <li>• Responsive design</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>User Journey Flow</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-3 p-4 bg-white rounded-lg border">
                    <div className="h-10 w-10 bg-amber-100 rounded-full flex items-center justify-center">
                      <span className="text-amber-600">1</span>
                    </div>
                    <div className="flex-1">
                      <h5 className="font-medium">Landing</h5>
                      <p className="text-sm text-gray-600">User arrives at home page with hero section</p>
                    </div>
                    <ArrowRight className="h-5 w-5 text-gray-400" />
                  </div>

                  <div className="flex items-center gap-3 p-4 bg-white rounded-lg border">
                    <div className="h-10 w-10 bg-amber-100 rounded-full flex items-center justify-center">
                      <span className="text-amber-600">2</span>
                    </div>
                    <div className="flex-1">
                      <h5 className="font-medium">Browse</h5>
                      <p className="text-sm text-gray-600">Explore tours, accommodations, and activities</p>
                    </div>
                    <ArrowRight className="h-5 w-5 text-gray-400" />
                  </div>

                  <div className="flex items-center gap-3 p-4 bg-white rounded-lg border">
                    <div className="h-10 w-10 bg-amber-100 rounded-full flex items-center justify-center">
                      <span className="text-amber-600">3</span>
                    </div>
                    <div className="flex-1">
                      <h5 className="font-medium">Select</h5>
                      <p className="text-sm text-gray-600">Choose preferred tour packages or accommodations</p>
                    </div>
                    <ArrowRight className="h-5 w-5 text-gray-400" />
                  </div>

                  <div className="flex items-center gap-3 p-4 bg-white rounded-lg border">
                    <div className="h-10 w-10 bg-amber-100 rounded-full flex items-center justify-center">
                      <span className="text-amber-600">4</span>
                    </div>
                    <div className="flex-1">
                      <h5 className="font-medium">Book</h5>
                      <p className="text-sm text-gray-600">Complete booking form with travel details</p>
                    </div>
                    <ArrowRight className="h-5 w-5 text-gray-400" />
                  </div>

                  <div className="flex items-center gap-3 p-4 bg-white rounded-lg border">
                    <div className="h-10 w-10 bg-amber-100 rounded-full flex items-center justify-center">
                      <span className="text-amber-600">5</span>
                    </div>
                    <div className="flex-1">
                      <h5 className="font-medium">Review</h5>
                      <p className="text-sm text-gray-600">Leave feedback after the trip</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Navigation Tab */}
          <TabsContent value="navigation" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Page Navigation Structure</CardTitle>
                <CardDescription>
                  All pages are accessible via the main navigation header
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow">
                    <div className="flex items-center gap-3 mb-2">
                      <Home className="h-5 w-5 text-amber-600" />
                      <h5 className="font-medium">Home Page</h5>
                    </div>
                    <p className="text-sm text-gray-600">
                      Hero section with search, featured tours, and accommodations
                    </p>
                  </div>

                  <div className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow">
                    <div className="flex items-center gap-3 mb-2">
                      <ShoppingCart className="h-5 w-5 text-amber-600" />
                      <h5 className="font-medium">Shop Page</h5>
                    </div>
                    <p className="text-sm text-gray-600">
                      E-commerce for souvenirs with add product functionality
                    </p>
                  </div>

                  <div className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow">
                    <div className="flex items-center gap-3 mb-2">
                      <Package className="h-5 w-5 text-amber-600" />
                      <h5 className="font-medium">Tour Package</h5>
                    </div>
                    <p className="text-sm text-gray-600">
                      Browse and filter safari packages and expeditions
                    </p>
                  </div>

                  <div className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow">
                    <div className="flex items-center gap-3 mb-2">
                      <Building className="h-5 w-5 text-amber-600" />
                      <h5 className="font-medium">Accommodation</h5>
                    </div>
                    <p className="text-sm text-gray-600">
                      Luxury lodges, camps, and resorts with ratings
                    </p>
                  </div>

                  <div className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow">
                    <div className="flex items-center gap-3 mb-2">
                      <Activity className="h-5 w-5 text-amber-600" />
                      <h5 className="font-medium">Activities</h5>
                    </div>
                    <p className="text-sm text-gray-600">
                      Safari activities, cultural tours, and adventures
                    </p>
                  </div>

                  <div className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow">
                    <div className="flex items-center gap-3 mb-2">
                      <Landmark className="h-5 w-5 text-amber-600" />
                      <h5 className="font-medium">Heritage</h5>
                    </div>
                    <p className="text-sm text-gray-600">
                      Cultural sites, traditions, and historical landmarks
                    </p>
                  </div>

                  <div className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow">
                    <div className="flex items-center gap-3 mb-2">
                      <Map className="h-5 w-5 text-amber-600" />
                      <h5 className="font-medium">Geography</h5>
                    </div>
                    <p className="text-sm text-gray-600">
                      National parks, regions, and tour destinations
                    </p>
                  </div>

                  <div className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow">
                    <div className="flex items-center gap-3 mb-2">
                      <Info className="h-5 w-5 text-amber-600" />
                      <h5 className="font-medium">About</h5>
                    </div>
                    <p className="text-sm text-gray-600">
                      Company information, mission, and team
                    </p>
                  </div>

                  <div className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow">
                    <div className="flex items-center gap-3 mb-2">
                      <Calendar className="h-5 w-5 text-amber-600" />
                      <h5 className="font-medium">Booking</h5>
                    </div>
                    <p className="text-sm text-gray-600">
                      Reservation form for tours and accommodations
                    </p>
                  </div>

                  <div className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow">
                    <div className="flex items-center gap-3 mb-2">
                      <Star className="h-5 w-5 text-amber-600" />
                      <h5 className="font-medium">Reviews</h5>
                    </div>
                    <p className="text-sm text-gray-600">
                      User reviews with add, rate, and like features
                    </p>
                  </div>

                  <div className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow">
                    <div className="flex items-center gap-3 mb-2">
                      <HelpCircle className="h-5 w-5 text-amber-600" />
                      <h5 className="font-medium">Support</h5>
                    </div>
                    <p className="text-sm text-gray-600">
                      FAQ, contact form, and help center
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Features Tab */}
          <TabsContent value="features" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Interactive Features</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="border-l-4 border-amber-600 pl-4">
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <ShoppingCart className="h-5 w-5" />
                    Shop - E-commerce Features
                  </h4>
                  <ul className="space-y-1 text-sm text-gray-600">
                    <li>✓ Add new products dynamically</li>
                    <li>✓ Product cards with images, ratings, and prices</li>
                    <li>✓ Add to cart functionality</li>
                    <li>✓ Product ratings display</li>
                    <li>✓ Responsive grid layout</li>
                  </ul>
                </div>

                <div className="border-l-4 border-amber-600 pl-4">
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    Reviews - User Engagement
                  </h4>
                  <ul className="space-y-1 text-sm text-gray-600">
                    <li>✓ Write and submit new reviews</li>
                    <li>✓ Star rating system (1-5 stars)</li>
                    <li>✓ Like/unlike reviews</li>
                    <li>✓ Dynamic like counter</li>
                    <li>✓ User avatars with initials</li>
                    <li>✓ Date stamping of reviews</li>
                  </ul>
                </div>

                <div className="border-l-4 border-amber-600 pl-4">
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <HelpCircle className="h-5 w-5" />
                    Support - Help Center
                  </h4>
                  <ul className="space-y-1 text-sm text-gray-600">
                    <li>✓ FAQ accordion with common questions</li>
                    <li>✓ Contact form with validation</li>
                    <li>✓ Multiple contact methods (phone, email, chat)</li>
                    <li>✓ Office hours display</li>
                    <li>✓ Toast notifications for form submissions</li>
                  </ul>
                </div>

                <div className="border-l-4 border-amber-600 pl-4">
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Navigation System
                  </h4>
                  <ul className="space-y-1 text-sm text-gray-600">
                    <li>✓ Client-side routing (no page reload)</li>
                    <li>✓ Active page highlighting</li>
                    <li>✓ Smooth scroll to top on navigation</li>
                    <li>✓ Mobile responsive menu</li>
                    <li>✓ Logo positioned on left (clickable to home)</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Wireframes Tab */}
          <TabsContent value="wireframes" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Application Wireframes</CardTitle>
                <CardDescription>Visual representation of page layouts and user flows</CardDescription>
              </CardHeader>
              <CardContent className="space-y-8">
                {/* Header Wireframe */}
                <div>
                  <h4 className="font-semibold mb-4">Header Layout</h4>
                  <div className="border rounded-lg p-4 bg-white">
                    <div className="flex items-center justify-between border-b pb-4">
                      <div className="p-2 bg-amber-100 rounded">
                        <span className="text-xs">Logo (Left)</span>
                      </div>
                      <div className="flex gap-2 text-xs">
                        <span className="p-2 bg-gray-100 rounded">Home</span>
                        <span className="p-2 bg-gray-100 rounded">Shop</span>
                        <span className="p-2 bg-gray-100 rounded">Tours</span>
                        <span className="p-2 bg-gray-100 rounded">Accommodation</span>
                        <span className="p-2 bg-gray-100 rounded">Activities</span>
                        <span className="p-2 bg-gray-100 rounded">More...</span>
                      </div>
                      <div className="flex gap-2">
                        <div className="h-8 w-8 bg-gray-100 rounded flex items-center justify-center">
                          <ShoppingCart className="h-4 w-4" />
                        </div>
                        <div className="h-8 w-8 bg-gray-100 rounded flex items-center justify-center">
                          <Users className="h-4 w-4" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Home Page Wireframe */}
                <div>
                  <h4 className="font-semibold mb-4">Home Page Structure</h4>
                  <div className="border rounded-lg p-4 bg-white space-y-4">
                    <div className="h-40 bg-gradient-to-r from-amber-200 to-amber-100 rounded flex items-center justify-center">
                      <span className="text-sm">Hero Section with Search Bar</span>
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <div className="h-24 bg-gray-100 rounded flex items-center justify-center text-xs">Tour Card</div>
                      <div className="h-24 bg-gray-100 rounded flex items-center justify-center text-xs">Tour Card</div>
                      <div className="h-24 bg-gray-100 rounded flex items-center justify-center text-xs">Tour Card</div>
                    </div>
                    <div className="grid grid-cols-4 gap-2">
                      <div className="h-20 bg-gray-100 rounded flex items-center justify-center text-xs">Lodge</div>
                      <div className="h-20 bg-gray-100 rounded flex items-center justify-center text-xs">Lodge</div>
                      <div className="h-20 bg-gray-100 rounded flex items-center justify-center text-xs">Lodge</div>
                      <div className="h-20 bg-gray-100 rounded flex items-center justify-center text-xs">Lodge</div>
                    </div>
                  </div>
                </div>

                {/* Shop Page Wireframe */}
                <div>
                  <h4 className="font-semibold mb-4">Shop Page Structure</h4>
                  <div className="border rounded-lg p-4 bg-white space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Shop Authentic Souvenirs</span>
                      <div className="px-3 py-1 bg-amber-600 text-white rounded text-xs">+ Add Product</div>
                    </div>
                    <div className="grid grid-cols-4 gap-2">
                      {[1, 2, 3, 4, 5, 6].map((i) => (
                        <div key={i} className="border rounded p-2 space-y-2">
                          <div className="h-20 bg-gray-200 rounded"></div>
                          <div className="h-3 bg-gray-100 rounded"></div>
                          <div className="flex justify-between items-center">
                            <div className="h-2 w-12 bg-amber-200 rounded"></div>
                            <div className="h-6 w-16 bg-amber-600 rounded text-xs"></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Reviews Page Wireframe */}
                <div>
                  <h4 className="font-semibold mb-4">Reviews Page Structure</h4>
                  <div className="border rounded-lg p-4 bg-white space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Guest Reviews & Comments</span>
                      <div className="px-3 py-1 bg-amber-600 text-white rounded text-xs">+ Write Review</div>
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      {[1, 2, 3].map((i) => (
                        <div key={i} className="border rounded p-3 space-y-2">
                          <div className="flex items-center gap-2">
                            <div className="h-8 w-8 bg-amber-600 rounded-full"></div>
                            <div className="space-y-1 flex-1">
                              <div className="h-2 bg-gray-200 rounded w-20"></div>
                              <div className="h-2 bg-gray-100 rounded w-16"></div>
                            </div>
                          </div>
                          <div className="flex gap-1">
                            {[1, 2, 3, 4, 5].map((s) => (
                              <Star key={s} className="h-3 w-3 fill-amber-500 text-amber-500" />
                            ))}
                          </div>
                          <div className="space-y-1">
                            <div className="h-2 bg-gray-100 rounded"></div>
                            <div className="h-2 bg-gray-100 rounded"></div>
                            <div className="h-2 bg-gray-100 rounded w-3/4"></div>
                          </div>
                          <div className="flex items-center gap-1 pt-2">
                            <ThumbsUp className="h-3 w-3" />
                            <span className="text-xs">12</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Support Page Wireframe */}
                <div>
                  <h4 className="font-semibold mb-4">Support Page Structure</h4>
                  <div className="border rounded-lg p-4 bg-white space-y-4">
                    <div className="grid grid-cols-3 gap-2">
                      <div className="border rounded p-2 text-center">
                        <Phone className="h-6 w-6 mx-auto mb-2 text-amber-600" />
                        <span className="text-xs">Phone Support</span>
                      </div>
                      <div className="border rounded p-2 text-center">
                        <Mail className="h-6 w-6 mx-auto mb-2 text-amber-600" />
                        <span className="text-xs">Email Support</span>
                      </div>
                      <div className="border rounded p-2 text-center">
                        <MessageSquare className="h-6 w-6 mx-auto mb-2 text-amber-600" />
                        <span className="text-xs">Live Chat</span>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <span className="text-xs font-medium">FAQ Section</span>
                        {[1, 2, 3, 4].map((i) => (
                          <div key={i} className="h-8 bg-gray-100 rounded"></div>
                        ))}
                      </div>
                      <div className="border rounded p-2 space-y-2">
                        <span className="text-xs font-medium">Contact Form</span>
                        <div className="h-6 bg-gray-100 rounded"></div>
                        <div className="h-6 bg-gray-100 rounded"></div>
                        <div className="h-16 bg-gray-100 rounded"></div>
                        <div className="h-8 bg-amber-600 rounded"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </section>
  );
}
