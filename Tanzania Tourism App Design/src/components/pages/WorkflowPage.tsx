import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle, CardDescription } from '../ui/card';
import { WireframeVisualization } from '../WireframeVisualization';
import { 
  Home, 
  ShoppingBag, 
  MapPin, 
  Calendar, 
  Star, 
  Heart,
  MessageCircle,
  Settings,
  Users,
  Book,
  Mountain,
  Landmark,
  HelpCircle,
  ArrowRight,
  CheckCircle
} from 'lucide-react';

export function WorkflowPage() {
  const pages = [
    {
      name: 'Home Page',
      icon: Home,
      description: 'Landing page with hero section, featured tours, and accommodations',
      features: [
        'Hero banner with search functionality',
        'Featured tour packages',
        'Luxury accommodations showcase',
        'Quick navigation to all sections'
      ]
    },
    {
      name: 'Tour Package Page',
      icon: Calendar,
      description: 'Browse and explore safari tour packages',
      features: [
        'Detailed tour package listings',
        'Filter by duration, price, and type',
        'View package details and itinerary',
        'Book tours directly'
      ]
    },
    {
      name: 'Accommodation Page',
      icon: MapPin,
      description: 'Explore luxury lodges and camps',
      features: [
        'Browse available accommodations',
        'View amenities and pricing',
        'Filter by location and rating',
        'Check availability and book'
      ]
    },
    {
      name: 'Activities Page',
      icon: Mountain,
      description: 'Discover available safari activities',
      features: [
        'Game drives and wildlife viewing',
        'Cultural experiences',
        'Adventure activities',
        'Activity scheduling'
      ]
    },
    {
      name: 'Heritage Page',
      icon: Landmark,
      description: 'Learn about Tanzanian culture and heritage',
      features: [
        'Cultural sites and traditions',
        'Historical information',
        'Maasai culture experiences',
        'Heritage tours'
      ]
    },
    {
      name: 'Geography Page',
      icon: Mountain,
      description: 'Explore Tanzania\'s geographical wonders',
      features: [
        'National parks information',
        'Mount Kilimanjaro details',
        'Serengeti and Ngorongoro',
        'Interactive maps and guides'
      ]
    },
    {
      name: 'Shop Page',
      icon: ShoppingBag,
      description: 'Purchase authentic Tanzanian souvenirs',
      features: [
        'Browse local crafts and products',
        'Add new products (admin)',
        'Product ratings and reviews',
        'Shopping cart functionality'
      ],
      interactive: true
    },
    {
      name: 'Reviews Page',
      icon: Star,
      description: 'Read and write customer reviews',
      features: [
        'View all guest reviews',
        'Write your own review',
        'Rate your experience (1-5 stars)',
        'Like and interact with reviews'
      ],
      interactive: true
    },
    {
      name: 'Booking Page',
      icon: Calendar,
      description: 'Complete your safari booking',
      features: [
        'Select dates and package',
        'Enter traveler information',
        'Review booking details',
        'Secure payment processing'
      ]
    },
    {
      name: 'About Page',
      icon: Users,
      description: 'Learn about SevenSerenity Safaris',
      features: [
        'Company history and mission',
        'Team information',
        'Awards and certifications',
        'Sustainability commitment'
      ]
    },
    {
      name: 'Support Page',
      icon: HelpCircle,
      description: 'Get help and customer support',
      features: [
        'FAQ section',
        'Contact form',
        'Live chat support',
        'Phone and email contacts'
      ]
    }
  ];

  const userJourney = [
    {
      step: 1,
      title: 'Discover',
      description: 'Browse homepage, explore tours and accommodations',
      icon: Home
    },
    {
      step: 2,
      title: 'Research',
      description: 'Read reviews, check geography and heritage information',
      icon: Book
    },
    {
      step: 3,
      title: 'Select',
      description: 'Choose tour package, accommodation, and activities',
      icon: CheckCircle
    },
    {
      step: 4,
      title: 'Book',
      description: 'Complete booking form and payment',
      icon: Calendar
    },
    {
      step: 5,
      title: 'Shop',
      description: 'Browse and purchase souvenirs',
      icon: ShoppingBag
    },
    {
      step: 6,
      title: 'Review',
      description: 'Share your experience and rate services',
      icon: Star
    }
  ];

  return (
    <div className="py-20 bg-gray-50 min-h-screen">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-5xl mb-4">App Workflow & Structure</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Complete guide to navigating the SevenSerenity Safaris tourism application
          </p>
        </div>

        {/* User Journey */}
        <section className="mb-16">
          <h2 className="text-3xl mb-8 text-center">User Journey</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
            {userJourney.map((journey) => (
              <Card key={journey.step} className="relative">
                <CardHeader className="text-center">
                  <div className="mx-auto h-12 w-12 bg-amber-100 rounded-full flex items-center justify-center mb-3">
                    <journey.icon className="h-6 w-6 text-amber-600" />
                  </div>
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 h-8 w-8 bg-amber-600 text-white rounded-full flex items-center justify-center">
                    {journey.step}
                  </div>
                  <CardTitle className="text-lg">{journey.title}</CardTitle>
                  <CardDescription className="text-sm">
                    {journey.description}
                  </CardDescription>
                </CardHeader>
              </Card>
            ))}
          </div>
        </section>

        {/* App Structure */}
        <section className="mb-16">
          <h2 className="text-3xl mb-8 text-center">Application Structure</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {pages.map((page) => (
              <Card key={page.name} className="hover:shadow-xl transition-shadow">
                <CardHeader>
                  <div className="flex items-start gap-3 mb-3">
                    <div className="h-10 w-10 bg-amber-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <page.icon className="h-5 w-5 text-amber-600" />
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-lg mb-1">{page.name}</CardTitle>
                      {page.interactive && (
                        <span className="inline-block text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                          Interactive
                        </span>
                      )}
                    </div>
                  </div>
                  <CardDescription>{page.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {page.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start gap-2 text-sm text-gray-600">
                        <CheckCircle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Navigation Flow */}
        <section className="mb-16">
          <h2 className="text-3xl mb-8 text-center">Navigation Flow</h2>
          <Card className="max-w-4xl mx-auto">
            <CardHeader>
              <CardTitle>How Navigation Works</CardTitle>
              <CardDescription>
                Understanding the application's navigation system
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="h-8 w-8 bg-amber-600 text-white rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    1
                  </div>
                  <div>
                    <h4 className="font-medium mb-1">Header Navigation</h4>
                    <p className="text-sm text-gray-600">
                      The top header contains all main navigation links. Click any menu item to navigate to that page. 
                      The logo on the left returns you to the home page.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="h-8 w-8 bg-amber-600 text-white rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    2
                  </div>
                  <div>
                    <h4 className="font-medium mb-1">Single Page Application</h4>
                    <p className="text-sm text-gray-600">
                      Navigation happens within the same page without refreshing the browser. 
                      This provides a smooth, app-like experience.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="h-8 w-8 bg-amber-600 text-white rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    3
                  </div>
                  <div>
                    <h4 className="font-medium mb-1">Interactive Features</h4>
                    <p className="text-sm text-gray-600">
                      On Shop and Reviews pages, users can add products, write reviews, rate experiences, 
                      and like content. All changes are stored in the application state.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="h-8 w-8 bg-amber-600 text-white rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    4
                  </div>
                  <div>
                    <h4 className="font-medium mb-1">Footer Links</h4>
                    <p className="text-sm text-gray-600">
                      The footer at the bottom of every page also contains quick links to major sections, 
                      providing alternative navigation options.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Wireframes */}
        <section className="mb-16">
          <h2 className="text-3xl mb-8 text-center">Page Wireframes & Prototypes</h2>
          <p className="text-center text-gray-600 mb-8 max-w-2xl mx-auto">
            Visual representation of how each page is structured and functions
          </p>
          <WireframeVisualization />
        </section>

        {/* Interactive Features */}
        <section>
          <h2 className="text-3xl mb-8 text-center">Interactive Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            <Card>
              <CardHeader>
                <div className="h-12 w-12 bg-amber-100 rounded-lg flex items-center justify-center mb-3">
                  <ShoppingBag className="h-6 w-6 text-amber-600" />
                </div>
                <CardTitle>Shop Management</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-start gap-2">
                  <ArrowRight className="h-4 w-4 text-amber-600 mt-1 flex-shrink-0" />
                  <p className="text-sm text-gray-600">Add new products with name, description, price, and image</p>
                </div>
                <div className="flex items-start gap-2">
                  <ArrowRight className="h-4 w-4 text-amber-600 mt-1 flex-shrink-0" />
                  <p className="text-sm text-gray-600">View product ratings and details</p>
                </div>
                <div className="flex items-start gap-2">
                  <ArrowRight className="h-4 w-4 text-amber-600 mt-1 flex-shrink-0" />
                  <p className="text-sm text-gray-600">Add items to shopping cart</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="h-12 w-12 bg-amber-100 rounded-lg flex items-center justify-center mb-3">
                  <Star className="h-6 w-6 text-amber-600" />
                </div>
                <CardTitle>Reviews & Ratings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-start gap-2">
                  <ArrowRight className="h-4 w-4 text-amber-600 mt-1 flex-shrink-0" />
                  <p className="text-sm text-gray-600">Write detailed reviews with your name and location</p>
                </div>
                <div className="flex items-start gap-2">
                  <ArrowRight className="h-4 w-4 text-amber-600 mt-1 flex-shrink-0" />
                  <p className="text-sm text-gray-600">Rate experiences from 1-5 stars</p>
                </div>
                <div className="flex items-start gap-2">
                  <ArrowRight className="h-4 w-4 text-amber-600 mt-1 flex-shrink-0" />
                  <p className="text-sm text-gray-600">Like reviews from other users</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="h-12 w-12 bg-amber-100 rounded-lg flex items-center justify-center mb-3">
                  <Calendar className="h-6 w-6 text-amber-600" />
                </div>
                <CardTitle>Booking System</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-start gap-2">
                  <ArrowRight className="h-4 w-4 text-amber-600 mt-1 flex-shrink-0" />
                  <p className="text-sm text-gray-600">Select tour packages and dates</p>
                </div>
                <div className="flex items-start gap-2">
                  <ArrowRight className="h-4 w-4 text-amber-600 mt-1 flex-shrink-0" />
                  <p className="text-sm text-gray-600">Enter traveler information</p>
                </div>
                <div className="flex items-start gap-2">
                  <ArrowRight className="h-4 w-4 text-amber-600 mt-1 flex-shrink-0" />
                  <p className="text-sm text-gray-600">Review and confirm bookings</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="h-12 w-12 bg-amber-100 rounded-lg flex items-center justify-center mb-3">
                  <HelpCircle className="h-6 w-6 text-amber-600" />
                </div>
                <CardTitle>Support System</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-start gap-2">
                  <ArrowRight className="h-4 w-4 text-amber-600 mt-1 flex-shrink-0" />
                  <p className="text-sm text-gray-600">Browse FAQ for common questions</p>
                </div>
                <div className="flex items-start gap-2">
                  <ArrowRight className="h-4 w-4 text-amber-600 mt-1 flex-shrink-0" />
                  <p className="text-sm text-gray-600">Submit support tickets via contact form</p>
                </div>
                <div className="flex items-start gap-2">
                  <ArrowRight className="h-4 w-4 text-amber-600 mt-1 flex-shrink-0" />
                  <p className="text-sm text-gray-600">Live chat with support team</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    </div>
  );
}
