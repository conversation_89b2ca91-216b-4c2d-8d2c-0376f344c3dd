import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, Card<PERSON>eader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { Label } from '../ui/label';
import { ShoppingCart, Star, Plus } from 'lucide-react';
import { ImageWithFallback } from '../figma/ImageWithFallback';

interface Product {
  id: number;
  name: string;
  description: string;
  image: string;
  price: string;
  rating: number;
}

const initialProducts: Product[] = [
  {
    id: 1,
    name: 'Maasai Beaded Necklace',
    description: 'Handcrafted traditional jewelry',
    image: 'https://images.unsplash.com/photo-1757140448293-fa0de8f449e5?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxBZnJpY2FuJTIwYmVhZGVkJTIwamV3ZWxyeXxlbnwxfHx8fDE3NjAyODA2MzB8MA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
    price: '$45',
    rating: 4.8,
  },
  {
    id: 2,
    name: 'African Art Sculpture',
    description: 'Hand-carved wooden figurine',
    image: 'https://images.unsplash.com/photo-1641582163466-e4d573078f98?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxBZnJpY2FuJTIwaGFuZGljcmFmdHN8ZW58MXx8fHwxNzYwMjgwNjI5fDA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
    price: '$85',
    rating: 4.9,
  },
  {
    id: 3,
    name: 'Tanzanite Gemstone',
    description: 'Authentic tanzanite jewelry',
    image: 'https://images.unsplash.com/photo-1757140448293-fa0de8f449e5?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxBZnJpY2FuJTIwYmVhZGVkJTIwamV3ZWxyeXxlbnwxfHx8fDE3NjAyODA2MzB8MA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
    price: '$250',
    rating: 5.0,
  },
];

export function ShopPage() {
  const [products, setProducts] = useState<Product[]>(initialProducts);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [productRatings, setProductRatings] = useState<{ [key: number]: number[] }>({});
  const [newProduct, setNewProduct] = useState({
    name: '',
    description: '',
    price: '',
    image: '',
  });

  const handleAddProduct = () => {
    if (newProduct.name && newProduct.description && newProduct.price) {
      const product: Product = {
        id: Date.now(),
        name: newProduct.name,
        description: newProduct.description,
        price: newProduct.price,
        image: newProduct.image || 'https://images.unsplash.com/photo-1641582163466-e4d573078f98?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxBZnJpY2FuJTIwaGFuZGljcmFmdHN8ZW58MXx8fHwxNzYwMjgwNjI5fDA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
        rating: 0,
      };
      setProducts([...products, product]);
      setNewProduct({ name: '', description: '', price: '', image: '' });
      setIsAddDialogOpen(false);
    }
  };

  const handleRateProduct = (productId: number, rating: number) => {
    const currentRatings = productRatings[productId] || [];
    const newRatings = [...currentRatings, rating];
    setProductRatings({ ...productRatings, [productId]: newRatings });
    
    // Update average rating
    const avgRating = newRatings.reduce((a, b) => a + b, 0) / newRatings.length;
    setProducts(products.map(p => 
      p.id === productId ? { ...p, rating: Number(avgRating.toFixed(1)) } : p
    ));
  };

  const getProductRating = (productId: number) => {
    const ratings = productRatings[productId];
    if (!ratings || ratings.length === 0) {
      const product = products.find(p => p.id === productId);
      return product?.rating || 0;
    }
    return Number((ratings.reduce((a, b) => a + b, 0) / ratings.length).toFixed(1));
  };

  return (
    <section className="py-20 bg-gray-50 min-h-screen">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl mb-4">Shop Authentic Souvenirs</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Take home a piece of Tanzania with our curated collection of local crafts and products
          </p>
        </div>

        <div className="flex justify-end mb-6">
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-amber-600 hover:bg-amber-700">
                <Plus className="h-4 w-4 mr-2" />
                Add New Product
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Product</DialogTitle>
                <DialogDescription>
                  Add a new product to the shop
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="product-name">Product Name</Label>
                  <Input
                    id="product-name"
                    value={newProduct.name}
                    onChange={(e) => setNewProduct({ ...newProduct, name: e.target.value })}
                    placeholder="Enter product name"
                  />
                </div>
                <div>
                  <Label htmlFor="product-description">Description</Label>
                  <Textarea
                    id="product-description"
                    value={newProduct.description}
                    onChange={(e) => setNewProduct({ ...newProduct, description: e.target.value })}
                    placeholder="Enter product description"
                  />
                </div>
                <div>
                  <Label htmlFor="product-price">Price</Label>
                  <Input
                    id="product-price"
                    value={newProduct.price}
                    onChange={(e) => setNewProduct({ ...newProduct, price: e.target.value })}
                    placeholder="$0.00"
                  />
                </div>
                <div>
                  <Label htmlFor="product-image">Image URL (optional)</Label>
                  <Input
                    id="product-image"
                    value={newProduct.image}
                    onChange={(e) => setNewProduct({ ...newProduct, image: e.target.value })}
                    placeholder="https://..."
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddProduct} className="bg-amber-600 hover:bg-amber-700">
                  Add Product
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {products.map((product) => (
            <Card key={product.id} className="overflow-hidden hover:shadow-xl transition-shadow flex flex-col">
              <div className="relative h-48">
                <ImageWithFallback
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <CardHeader className="flex-1">
                <CardTitle className="text-base">{product.name}</CardTitle>
                <p className="text-sm text-gray-600">{product.description}</p>
                <div className="flex items-center gap-1 mt-2">
                  <Star className="h-3 w-3 fill-amber-500 text-amber-500" />
                  <span className="text-sm">{getProductRating(product.id)}</span>
                  <span className="text-xs text-gray-500 ml-1">
                    ({productRatings[product.id]?.length || 0} ratings)
                  </span>
                </div>
                <div className="flex gap-1 mt-2">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      onClick={() => handleRateProduct(product.id, star)}
                      className="hover:scale-110 transition-transform"
                      title={`Rate ${star} stars`}
                    >
                      <Star
                        className={`h-4 w-4 cursor-pointer ${
                          star <= getProductRating(product.id)
                            ? 'fill-amber-500 text-amber-500'
                            : 'text-gray-300'
                        }`}
                      />
                    </button>
                  ))}
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-xl text-amber-600">{product.price}</p>
              </CardContent>
              <CardFooter>
                <Button className="w-full bg-amber-600 hover:bg-amber-700">
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Add to Cart
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
