import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Award, Users, Globe, Heart } from 'lucide-react';
import { ImageWithFallback } from '../figma/ImageWithFallback';

export function AboutPage() {
  return (
    <section className="py-20 bg-white min-h-screen">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h2 className="text-4xl mb-4">About SevenSerenity Safaris</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Creating unforgettable African safari experiences since 2015
          </p>
        </div>

        {/* Story Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          <div>
            <h3 className="text-3xl mb-6">Our Story</h3>
            <div className="space-y-4 text-gray-600">
              <p>
                Founded in 2015, SevenSerenity Safaris was born from a deep passion for Tanzania's 
                incredible wildlife and a commitment to sharing its beauty with the world. Our name 
                reflects our promise to deliver seven pillars of serenity: authentic experiences, 
                expert guidance, luxury comfort, conservation ethics, cultural respect, personalized 
                service, and unforgettable memories.
              </p>
              <p>
                What started as a small family business has grown into one of Tanzania's most trusted 
                safari operators, hosting thousands of guests from around the globe. Yet, we've never 
                lost sight of what makes us special - our intimate knowledge of the land, our 
                relationships with local communities, and our unwavering dedication to creating 
                life-changing experiences.
              </p>
              <p>
                Every member of our team is passionate about Tanzania's natural wonders and cultural 
                heritage. From our expert guides who share their deep knowledge of wildlife behavior, 
                to our support staff who ensure every detail is perfect, we all share a common goal: 
                to make your safari dreams come true.
              </p>
            </div>
          </div>
          <div className="relative h-96 lg:h-auto">
            <ImageWithFallback
              src="https://images.unsplash.com/photo-1667550507974-cc647990b75a?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxUYW56YW5pYSUyMHNhZmFyaSUyMHdpbGRsaWZlfGVufDF8fHx8MTc2MDI4MDYyOHww&ixlib=rb-4.1.0&q=80&w=1080"
              alt="Safari landscape"
              className="w-full h-full object-cover rounded-lg shadow-lg"
            />
          </div>
        </div>

        {/* Values */}
        <div className="mb-16">
          <h3 className="text-3xl text-center mb-12">Our Values</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="h-16 w-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Award className="h-8 w-8 text-amber-600" />
                </div>
                <CardTitle>Excellence</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  We strive for excellence in every aspect of our service, from planning to execution.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="h-16 w-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Heart className="h-8 w-8 text-amber-600" />
                </div>
                <CardTitle>Conservation</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  We're committed to sustainable tourism and protecting Tanzania's natural heritage.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="h-16 w-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8 text-amber-600" />
                </div>
                <CardTitle>Community</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  We support local communities and ensure tourism benefits everyone.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="h-16 w-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Globe className="h-8 w-8 text-amber-600" />
                </div>
                <CardTitle>Authenticity</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  We offer genuine, immersive experiences that connect you with the real Tanzania.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Stats */}
        <div className="bg-amber-50 rounded-lg p-12">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl text-amber-600 mb-2">10,000+</div>
              <div className="text-gray-600">Happy Guests</div>
            </div>
            <div>
              <div className="text-4xl text-amber-600 mb-2">500+</div>
              <div className="text-gray-600">Safari Tours</div>
            </div>
            <div>
              <div className="text-4xl text-amber-600 mb-2">50+</div>
              <div className="text-gray-600">Expert Guides</div>
            </div>
            <div>
              <div className="text-4xl text-amber-600 mb-2">9+</div>
              <div className="text-gray-600">Years Experience</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
