import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Label } from '../ui/label';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '../ui/accordion';
import { MessageCircle, Mail, Phone, MapPin, Clock, Send } from 'lucide-react';
import { toast } from 'sonner';

export function SupportPage() {
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (contactForm.name && contactForm.email && contactForm.message) {
      toast.success('Message sent successfully! We\'ll get back to you soon.');
      setContactForm({ name: '', email: '', subject: '', message: '' });
    }
  };

  return (
    <section className="py-20 bg-gray-50 min-h-screen">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl mb-4">Support & Help Center</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            We're here to help! Find answers to common questions or get in touch with our team
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {/* Contact Cards */}
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="h-12 w-12 bg-amber-100 rounded-full flex items-center justify-center mb-4">
                <Phone className="h-6 w-6 text-amber-600" />
              </div>
              <CardTitle>Phone Support</CardTitle>
              <CardDescription>Available 24/7 for urgent inquiries</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-lg">+255 123 456 789</p>
              <p className="text-sm text-gray-500 mt-1">International: ****** 123 4567</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="h-12 w-12 bg-amber-100 rounded-full flex items-center justify-center mb-4">
                <Mail className="h-6 w-6 text-amber-600" />
              </div>
              <CardTitle>Email Support</CardTitle>
              <CardDescription>Response within 24 hours</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-lg"><EMAIL></p>
              <p className="text-sm text-gray-500 mt-1"><EMAIL></p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="h-12 w-12 bg-amber-100 rounded-full flex items-center justify-center mb-4">
                <MessageCircle className="h-6 w-6 text-amber-600" />
              </div>
              <CardTitle>Live Chat</CardTitle>
              <CardDescription>Chat with our team in real-time</CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="bg-amber-600 hover:bg-amber-700 w-full">
                Start Chat
              </Button>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* FAQ Section */}
          <div>
            <h3 className="text-2xl mb-6">Frequently Asked Questions</h3>
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="item-1">
                <AccordionTrigger>What is the best time to visit Tanzania?</AccordionTrigger>
                <AccordionContent>
                  The best time to visit Tanzania depends on what you want to see. The dry season (June to October) is ideal for wildlife viewing, especially the Great Migration in Serengeti. The wet season (November to May) offers lush landscapes and fewer tourists.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-2">
                <AccordionTrigger>Do I need a visa to visit Tanzania?</AccordionTrigger>
                <AccordionContent>
                  Most visitors need a visa to enter Tanzania. You can obtain a visa on arrival at the airport or apply for an e-visa online before your trip. Check with your local embassy for specific requirements based on your nationality.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-3">
                <AccordionTrigger>What vaccinations do I need?</AccordionTrigger>
                <AccordionContent>
                  Yellow fever vaccination is required if you're traveling from or through a yellow fever endemic country. We also recommend vaccinations for Hepatitis A, Typhoid, and malaria prophylaxis. Consult your doctor at least 4-6 weeks before travel.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-4">
                <AccordionTrigger>What should I pack for a safari?</AccordionTrigger>
                <AccordionContent>
                  Pack light, neutral-colored clothing (avoid bright colors and black), comfortable walking shoes, a hat, sunglasses, sunscreen, insect repellent, binoculars, and a camera. Layered clothing is recommended as temperatures can vary throughout the day.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-5">
                <AccordionTrigger>Can I customize my tour package?</AccordionTrigger>
                <AccordionContent>
                  Absolutely! All our tour packages can be customized to fit your preferences, budget, and schedule. Contact our team to discuss your specific requirements and we'll create a personalized itinerary just for you.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-6">
                <AccordionTrigger>What is your cancellation policy?</AccordionTrigger>
                <AccordionContent>
                  Cancellations made 60+ days before departure receive a full refund minus a 10% processing fee. Cancellations 30-59 days before receive 50% refund. Cancellations within 30 days are non-refundable. We recommend purchasing travel insurance.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>

          {/* Contact Form */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Send Us a Message</CardTitle>
                <CardDescription>
                  Have a question? Fill out the form below and we'll get back to you soon
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      value={contactForm.name}
                      onChange={(e) => setContactForm({ ...contactForm, name: e.target.value })}
                      placeholder="Your name"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={contactForm.email}
                      onChange={(e) => setContactForm({ ...contactForm, email: e.target.value })}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="subject">Subject</Label>
                    <Input
                      id="subject"
                      value={contactForm.subject}
                      onChange={(e) => setContactForm({ ...contactForm, subject: e.target.value })}
                      placeholder="How can we help?"
                    />
                  </div>
                  <div>
                    <Label htmlFor="message">Message</Label>
                    <Textarea
                      id="message"
                      value={contactForm.message}
                      onChange={(e) => setContactForm({ ...contactForm, message: e.target.value })}
                      placeholder="Tell us more about your inquiry..."
                      rows={5}
                      required
                    />
                  </div>
                  <Button type="submit" className="w-full bg-amber-600 hover:bg-amber-700">
                    <Send className="h-4 w-4 mr-2" />
                    Send Message
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Office Hours */}
            <Card className="mt-6">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-amber-600" />
                  <CardTitle>Office Hours</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Monday - Friday:</span>
                    <span>8:00 AM - 6:00 PM EAT</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Saturday:</span>
                    <span>9:00 AM - 4:00 PM EAT</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Sunday:</span>
                    <span>Closed</span>
                  </div>
                </div>
                <div className="mt-4 pt-4 border-t">
                  <div className="flex items-start gap-2 text-sm text-gray-600">
                    <MapPin className="h-4 w-4 mt-1 flex-shrink-0" />
                    <span>Arusha Office: Sokoine Road, Arusha, Tanzania</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}
