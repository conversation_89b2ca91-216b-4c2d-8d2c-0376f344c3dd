import { Menu, X, ShoppingCart, User } from 'lucide-react';
import { Button } from './ui/button';
import { useState } from 'react';

interface HeaderProps {
  currentPage: string;
  onNavigate: (page: string) => void;
}

export function Header({ currentPage, onNavigate }: HeaderProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const navItems = [
    { label: 'Home', page: 'home' },
    { label: 'Shop', page: 'shop' },
    { label: 'Tour Package', page: 'tour-package' },
    { label: 'Accommodation', page: 'accommodation' },
    { label: 'Activities', page: 'activities' },
    { label: 'Heritage', page: 'heritage' },
    { label: 'Geography', page: 'geography' },
    { label: 'About', page: 'about' },
    { label: 'Booking', page: 'booking' },
    { label: 'Reviews', page: 'reviews' },
    { label: 'Support', page: 'support' },
  ];

  const handleNavClick = (page: string) => {
    onNavigate(page);
    setMobileMenuOpen(false);
  };

  return (
    <header className="sticky top-0 z-50 w-full bg-white shadow-md">
      <div className="container mx-auto px-4">
        <div className="flex h-20 items-center justify-between">
          {/* Logo - Left Side */}
          <div 
            className="flex items-center gap-2 cursor-pointer" 
            onClick={() => onNavigate('home')}
          >
            <div className="text-left">
              <h1 className="text-amber-600 tracking-tight">SevenSerenity</h1>
              <p className="text-sm text-gray-600">Safaris</p>
            </div>
          </div>

          {/* Desktop Navigation - Center/Right */}
          <nav className="hidden lg:flex items-center space-x-6 flex-1 justify-end">
            {navItems.map((item) => (
              <button
                key={item.page}
                onClick={() => handleNavClick(item.page)}
                className={`text-gray-700 hover:text-amber-600 transition-colors ${
                  currentPage === item.page ? 'text-amber-600 font-medium' : ''
                }`}
              >
                {item.label}
              </button>
            ))}
          </nav>

          {/* Right Icons */}
          <div className="flex items-center gap-2 ml-6">
            <Button variant="ghost" size="icon" onClick={() => onNavigate('shop')}>
              <ShoppingCart className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon" className="hidden lg:flex">
              <User className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <nav className="lg:hidden py-4 border-t">
            <div className="flex flex-col space-y-4">
              {navItems.map((item) => (
                <button
                  key={item.page}
                  onClick={() => handleNavClick(item.page)}
                  className={`text-left text-gray-700 hover:text-amber-600 transition-colors ${
                    currentPage === item.page ? 'text-amber-600 font-medium' : ''
                  }`}
                >
                  {item.label}
                </button>
              ))}
              <button
                onClick={() => handleNavClick('login')}
                className="text-left text-gray-700 hover:text-amber-600 transition-colors flex items-center gap-2"
              >
                <User className="h-4 w-4" />
                Login
              </button>
            </div>
          </nav>
        )}
      </div>
    </header>
  );
}
