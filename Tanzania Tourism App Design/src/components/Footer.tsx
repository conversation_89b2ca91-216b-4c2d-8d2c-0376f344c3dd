import { Facebook, Instagram, Twitter, Mail, Phone, MapPin } from 'lucide-react';
import { Button } from './ui/button';

interface FooterProps {
  onNavigate: (page: string) => void;
}

export function Footer({ onNavigate }: FooterProps) {
  return (
    <footer className="bg-gray-900 text-gray-300">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
          {/* Company Info */}
          <div>
            <h3 className="text-white text-xl mb-4">SevenSerenity Safaris</h3>
            <p className="text-sm mb-4">
              Your trusted partner for unforgettable Tanzania safari experiences since 2015.
            </p>
            <div className="flex gap-3">
              <Button size="icon" variant="ghost" className="hover:bg-amber-600 hover:text-white">
                <Facebook className="h-5 w-5" />
              </Button>
              <Button size="icon" variant="ghost" className="hover:bg-amber-600 hover:text-white">
                <Instagram className="h-5 w-5" />
              </Button>
              <Button size="icon" variant="ghost" className="hover:bg-amber-600 hover:text-white">
                <Twitter className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-white mb-4">Quick Links</h4>
            <ul className="space-y-2 text-sm">
              <li><button onClick={() => onNavigate('home')} className="hover:text-amber-600 transition-colors">Home</button></li>
              <li><button onClick={() => onNavigate('tour-package')} className="hover:text-amber-600 transition-colors">Tour Packages</button></li>
              <li><button onClick={() => onNavigate('accommodation')} className="hover:text-amber-600 transition-colors">Accommodations</button></li>
              <li><button onClick={() => onNavigate('activities')} className="hover:text-amber-600 transition-colors">Activities</button></li>
              <li><button onClick={() => onNavigate('shop')} className="hover:text-amber-600 transition-colors">Shop</button></li>
              <li><button onClick={() => onNavigate('support')} className="hover:text-amber-600 transition-colors">Support</button></li>
              <li><button onClick={() => onNavigate('workflow')} className="hover:text-amber-600 transition-colors">App Workflow</button></li>
            </ul>
          </div>

          {/* Destinations */}
          <div>
            <h4 className="text-white mb-4">Popular Destinations</h4>
            <ul className="space-y-2 text-sm">
              <li><button onClick={() => onNavigate('geography')} className="hover:text-amber-600 transition-colors">Serengeti National Park</button></li>
              <li><button onClick={() => onNavigate('geography')} className="hover:text-amber-600 transition-colors">Mount Kilimanjaro</button></li>
              <li><button onClick={() => onNavigate('geography')} className="hover:text-amber-600 transition-colors">Ngorongoro Crater</button></li>
              <li><button onClick={() => onNavigate('geography')} className="hover:text-amber-600 transition-colors">Zanzibar</button></li>
              <li><button onClick={() => onNavigate('geography')} className="hover:text-amber-600 transition-colors">Tarangire</button></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-white mb-4">Contact Us</h4>
            <ul className="space-y-3 text-sm">
              <li className="flex items-start gap-2">
                <MapPin className="h-4 w-4 mt-1 flex-shrink-0" />
                <span>Arusha, Tanzania, East Africa</span>
              </li>
              <li className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                <span>+255 123 456 789</span>
              </li>
              <li className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 pt-8 text-center text-sm">
          <p>&copy; 2024 SevenSerenity Safaris. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}
