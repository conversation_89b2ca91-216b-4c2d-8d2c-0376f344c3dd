import { Card, CardContent, CardDescription, CardFooter, <PERSON><PERSON>eader, CardTitle } from './ui/card';
import { But<PERSON> } from './ui/button';
import { Badge } from './ui/badge';
import { MapPin, Calendar, Users } from 'lucide-react';
import { ImageWithFallback } from './figma/ImageWithFallback';

const packages = [
  {
    id: 1,
    title: 'Serengeti Safari Adventure',
    description: 'Witness the great migration and explore the vast plains of Serengeti',
    image: 'https://images.unsplash.com/photo-1464388334095-4e05c18297cd?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxTZXJlbmdldGklMjBsYW5kc2NhcGV8ZW58MXx8fHwxNzYwMjgwNjMwfDA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
    duration: '7 Days',
    groupSize: '2-8 People',
    price: '$2,499',
    badge: 'Popular',
  },
  {
    id: 2,
    title: 'Kilimanjaro Climbing Expedition',
    description: 'Conquer Africa\'s highest peak with expert guides',
    image: 'https://images.unsplash.com/photo-1489392191049-fc10c97e64b6?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxNb3VudCUyMEtpbGltYW5qYXJvfGVufDF8fHx8MTc2MDI4MDYzMHww&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
    duration: '8 Days',
    groupSize: '4-12 People',
    price: '$1,899',
    badge: 'Adventure',
  },
  {
    id: 3,
    title: 'Wildlife & Culture Safari',
    description: 'Combine wildlife viewing with visits to traditional Maasai villages',
    image: 'https://images.unsplash.com/photo-1667550507974-cc647990b75a?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxUYW56YW5pYSUyMHNhZmFyaSUyMHdpbGRsaWZlfGVufDF8fHx8MTc2MDI4MDYyOHww&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
    duration: '5 Days',
    groupSize: '2-6 People',
    price: '$1,599',
    badge: 'Cultural',
  },
];

export function TourPackages() {
  return (
    <section id="tour-package" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl mb-4">Tour Packages</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Choose from our carefully curated safari experiences
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {packages.map((pkg) => (
            <Card key={pkg.id} className="overflow-hidden hover:shadow-xl transition-shadow">
              <div className="relative h-64">
                <ImageWithFallback
                  src={pkg.image}
                  alt={pkg.title}
                  className="w-full h-full object-cover"
                />
                <Badge className="absolute top-4 right-4 bg-amber-600">
                  {pkg.badge}
                </Badge>
              </div>
              <CardHeader>
                <CardTitle>{pkg.title}</CardTitle>
                <CardDescription>{pkg.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-gray-600">
                    <Calendar className="h-4 w-4" />
                    <span className="text-sm">{pkg.duration}</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-600">
                    <Users className="h-4 w-4" />
                    <span className="text-sm">{pkg.groupSize}</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-600">
                    <MapPin className="h-4 w-4" />
                    <span className="text-sm">Tanzania</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between items-center">
                <div>
                  <span className="text-sm text-gray-600">From</span>
                  <p className="text-2xl text-amber-600">{pkg.price}</p>
                </div>
                <Button className="bg-amber-600 hover:bg-amber-700">Book Now</Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
