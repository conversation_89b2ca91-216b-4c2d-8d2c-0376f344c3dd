import { <PERSON>, CardContent, CardFooter, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { ShoppingC<PERSON>, Star } from 'lucide-react';
import { ImageWithFallback } from './figma/ImageWithFallback';

const products = [
  {
    id: 1,
    name: 'Maasai Beaded Necklace',
    description: 'Handcrafted traditional jewelry',
    image: 'https://images.unsplash.com/photo-1757140448293-fa0de8f449e5?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxBZnJpY2FuJTIwYmVhZGVkJTIwamV3ZWxyeXxlbnwxfHx8fDE3NjAyODA2MzB8MA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
    price: '$45',
    rating: 4.8,
  },
  {
    id: 2,
    name: 'African Art Sculpture',
    description: 'Hand-carved wooden figurine',
    image: 'https://images.unsplash.com/photo-1641582163466-e4d573078f98?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxBZnJpY2FuJTIwaGFuZGljcmFmdHN8ZW58MXx8fHwxNzYwMjgwNjI5fDA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
    price: '$85',
    rating: 4.9,
  },
  {
    id: 3,
    name: 'Tanzanite Gemstone',
    description: 'Authentic tanzanite jewelry',
    image: 'https://images.unsplash.com/photo-1757140448293-fa0de8f449e5?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxBZnJpY2FuJTIwYmVhZGVkJTIwamV3ZWxyeXxlbnwxfHx8fDE3NjAyODA2MzB8MA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
    price: '$250',
    rating: 5.0,
  },
  {
    id: 4,
    name: 'Kanga Fabric',
    description: 'Traditional East African cloth',
    image: 'https://images.unsplash.com/photo-1641582163466-e4d573078f98?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxBZnJpY2FuJTIwaGFuZGljcmFmdHN8ZW58MXx8fHwxNzYwMjgwNjI5fDA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
    price: '$35',
    rating: 4.7,
  },
  {
    id: 5,
    name: 'Safari Photography Book',
    description: 'Stunning wildlife photography',
    image: 'https://images.unsplash.com/photo-1641582163466-e4d573078f98?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxBZnJpY2FuJTIwaGFuZGljcmFmdHN8ZW58MXx8fHwxNzYwMjgwNjI5fDA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
    price: '$55',
    rating: 4.6,
  },
  {
    id: 6,
    name: 'Coffee from Kilimanjaro',
    description: 'Premium Tanzanian coffee beans',
    image: 'https://images.unsplash.com/photo-1641582163466-e4d573078f98?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxBZnJpY2FuJTIwaGFuZGljcmFmdHN8ZW58MXx8fHwxNzYwMjgwNjI5fDA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
    price: '$28',
    rating: 4.8,
  },
];

export function Shopping() {
  return (
    <section id="shop" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl mb-4">Shop Authentic Souvenirs</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Take home a piece of Tanzania with our curated collection of local crafts and products
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
          {products.map((product) => (
            <Card key={product.id} className="overflow-hidden hover:shadow-xl transition-shadow flex flex-col">
              <div className="relative h-48">
                <ImageWithFallback
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <CardHeader className="flex-1">
                <CardTitle className="text-base">{product.name}</CardTitle>
                <p className="text-sm text-gray-600">{product.description}</p>
                <div className="flex items-center gap-1 mt-2">
                  <Star className="h-3 w-3 fill-amber-500 text-amber-500" />
                  <span className="text-sm">{product.rating}</span>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-xl text-amber-600">{product.price}</p>
              </CardContent>
              <CardFooter>
                <Button className="w-full bg-amber-600 hover:bg-amber-700">
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Add to Cart
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
