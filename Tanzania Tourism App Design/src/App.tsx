import { useState } from 'react';
import { Header } from './components/Header';
import { Footer } from './components/Footer';
import { HomePage } from './components/pages/HomePage';
import { ShopPage } from './components/pages/ShopPage';
import { TourPackagePage } from './components/pages/TourPackagePage';
import { AccommodationPage } from './components/pages/AccommodationPage';
import { ActivitiesPage } from './components/pages/ActivitiesPage';
import { HeritagePage } from './components/pages/HeritagePage';
import { GeographyPage } from './components/pages/GeographyPage';
import { AboutPage } from './components/pages/AboutPage';
import { BookingPage } from './components/pages/BookingPage';
import { ReviewsPage } from './components/pages/ReviewsPage';
import { SupportPage } from './components/pages/SupportPage';
import { WorkflowPage } from './components/pages/WorkflowPage';
import { Toaster } from './components/ui/sonner';

export default function App() {
  const [currentPage, setCurrentPage] = useState('home');

  const handleNavigate = (page: string) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const renderPage = () => {
    switch (currentPage) {
      case 'home':
        return <HomePage onNavigate={handleNavigate} />;
      case 'shop':
        return <ShopPage />;
      case 'tour-package':
        return <TourPackagePage onNavigate={handleNavigate} />;
      case 'accommodation':
        return <AccommodationPage onNavigate={handleNavigate} />;
      case 'activities':
        return <ActivitiesPage onNavigate={handleNavigate} />;
      case 'heritage':
        return <HeritagePage onNavigate={handleNavigate} />;
      case 'geography':
        return <GeographyPage onNavigate={handleNavigate} />;
      case 'about':
        return <AboutPage onNavigate={handleNavigate} />;
      case 'booking':
        return <BookingPage />;
      case 'reviews':
        return <ReviewsPage />;
      case 'support':
        return <SupportPage />;
      case 'workflow':
        return <WorkflowPage />;
      default:
        return <HomePage onNavigate={handleNavigate} />;
    }
  };

  return (
    <div className="min-h-screen">
      <Header currentPage={currentPage} onNavigate={handleNavigate} />
      <main>
        {renderPage()}
      </main>
      <Footer onNavigate={handleNavigate} />
      <Toaster />
    </div>
  );
}
