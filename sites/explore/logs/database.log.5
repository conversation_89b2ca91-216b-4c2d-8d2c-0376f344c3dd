2025-05-21 11:51:55,386 WARNING database DDL Query made to DB:
create table `tabLRPMT Returns` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`appointment` varchar(140),
`company` varchar(140),
`inpatient_record` varchar(140),
`status` varchar(140),
`admitted_datetime` datetime(6),
`requested_by` varchar(140),
`approved_by` varchar(140),
`naming_series` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:55,570 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` ADD COLUMN `lab_test` varchar(140), ADD COLUMN `department_hsu` varchar(140), ADD COLUMN `intent` varchar(140), ADD COLUMN `sequence` int(11) not null default 0, ADD COLUMN `as_needed` int(1) not null default 0, ADD COLUMN `patient_instruction` text, ADD COLUMN `replaces` varchar(140), ADD COLUMN `priority` varchar(140), ADD COLUMN `note` text
2025-05-21 11:51:55,746 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`company` varchar(140),
`healthcare_service_order_category` varchar(140),
`patient_care_type` varchar(140),
`status` varchar(140) default 'Draft',
`order_date` date,
`ordered_by` varchar(140),
`order_group` varchar(140),
`replaces` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`triage` varchar(140),
`gender` varchar(140),
`birth_date` date,
`age` int(11) not null default 0,
`blood_group` varchar(140),
`marital_status` varchar(140),
`occupation` varchar(140),
`email` varchar(140),
`mobile` varchar(140),
`insurance_subscription` varchar(140),
`insurance_claim` varchar(140),
`insurance_company` varchar(140),
`claim_status` varchar(140),
`order_doctype` varchar(140),
`order` varchar(140),
`billing_item` varchar(140),
`invoiced` int(1) not null default 0,
`order_description` text,
`intent` varchar(140),
`priority` varchar(140),
`reason` varchar(140),
`reason_reference_doctype` varchar(140),
`reason_reference` varchar(140),
`quantity` int(11) not null default 0,
`sequence` int(11) not null default 0,
`expected_date` date,
`as_needed` int(1) not null default 0,
`occurrence` datetime(6),
`occurence_period` decimal(21,9),
`body_part` varchar(140),
`staff_role` varchar(140),
`healthcare_service_unit_type` varchar(140),
`note` text,
`patient_instruction` text,
`source` varchar(140),
`referring_practitioner` varchar(140),
`medical_code_standard` varchar(140),
`medical_code` varchar(140),
`order_reference_doctype` varchar(140),
`order_reference_name` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:55,902 WARNING database DDL Query made to DB:
create table `tabDrug Interaction` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication_class` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:56,699 WARNING database DDL Query made to DB:
create table `tabNHIF Product` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`product_id` varchar(140) unique,
`product_name` varchar(140),
`healthcare_insurance_coverage_plan` varchar(140),
`nhif_product_code` varchar(140),
`company` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:56,958 WARNING database DDL Query made to DB:
create table `tabHealthcare Package` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`package_name` varchar(140) unique,
`disabled` int(1) not null default 0,
`package_image` text,
`description` longtext,
`price_list` varchar(140),
`total_actual_item_price` decimal(21,9) not null default 0,
`total_price_of_services` decimal(21,9) not null default 0,
`price_of_package` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:57,116 WARNING database DDL Query made to DB:
create table `tabPrevious Radiology Procedure Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`radiology_examination_template` varchar(140),
`medical_code` varchar(140),
`override_subscription` int(1) not null default 0,
`prescribe` int(1) not null default 0,
`is_not_available_inhouse` int(1) not null default 0,
`radiology_procedure_name` varchar(140),
`invoiced` int(1) not null default 0,
`radiology_test_comment` text,
`radiology_examination_created` int(1) not null default 0,
`appointment_booked` int(1) not null default 0,
`radiology_examination` varchar(140),
`department_hsu` varchar(140),
`intent` varchar(140),
`sequence` int(11) not null default 0,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`body_part` varchar(140),
`note` text,
`delivered_quantity` decimal(21,9) not null default 0,
`sales_invoice_number` varchar(140),
`reference_journal_entry` varchar(140),
`cancelled` int(1) not null default 0,
index `invoiced`(`invoiced`),
index `radiology_examination_created`(`radiology_examination_created`),
index `appointment_booked`(`appointment_booked`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:57,253 WARNING database DDL Query made to DB:
create table `tabCompany NHIF Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140) unique,
`username` varchar(140),
`password` text,
`facility_code` varchar(140),
`enable` int(1) not null default 0,
`validate_service_approval_number_on_lrpm_documents` int(1) not null default 1,
`enable_auto_submit_of_claims` int(1) not null default 0,
`nhifservice_url` varchar(140),
`nhifservice_token` text,
`nhifservice_expiry` datetime(6),
`claimsserver_url` varchar(140),
`claimsserver_token` text,
`claimsserver_expiry` datetime(6),
`nhifform_url` varchar(140),
`nhifform_expiry` datetime(6),
`nhifform_token` text,
`update_patient_history` int(1) not null default 1,
`submit_claim_month` int(11) not null default 0,
`submit_claim_year` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:57,365 WARNING database DDL Query made to DB:
create table `tabNHIF Facility Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`facility_code` varchar(140) unique,
`facility_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:57,486 WARNING database DDL Query made to DB:
create table `tabVC Cash Rate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`vc_rate` decimal(21,9) not null default 0,
`company_rate` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:57,626 WARNING database DDL Query made to DB:
create table `tabNHIF Claim Reconciliation Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`billno` varchar(140),
`foliono` varchar(140),
`datesubmitted` datetime(6),
`cardno` varchar(140),
`authorizationno` varchar(140),
`amountclaimed` decimal(21,9) not null default 0,
`submissionid` varchar(140),
`submissionno` varchar(140),
`remarks` text,
index `billno`(`billno`),
index `foliono`(`foliono`),
index `cardno`(`cardno`),
index `authorizationno`(`authorizationno`),
index `amountclaimed`(`amountclaimed`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:57,726 WARNING database DDL Query made to DB:
create table `tabVC Practitioner` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`healathcare_practitioner` varchar(140),
`wtax` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:57,892 WARNING database DDL Query made to DB:
create table `tabNHIF Patient Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`amended_from` varchar(140),
`patient_appointment` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`first_name` varchar(140),
`last_name` varchar(140),
`telephone_no` varchar(140),
`date_of_birth` date,
`gender` varchar(140),
`allow_changes` int(1) not null default 0,
`company` varchar(140),
`posting_date` date,
`inpatient_record` varchar(140),
`cardno` varchar(140),
`authorization_no` varchar(140),
`coverage_plan_name` varchar(140),
`total_amount` decimal(21,9) not null default 0,
`patient_signature` longtext,
`is_ready_for_auto_submission` int(1) not null default 0,
`reviewed_by` varchar(140),
`folio_id` varchar(140),
`facility_code` varchar(140),
`claim_year` int(11) not null default 0,
`claim_month` int(11) not null default 0,
`folio_no` int(11) not null default 0,
`serial_no` varchar(140),
`created_by` varchar(140),
`item_crt_by` varchar(140),
`delayreason` text,
`practitioner_name` varchar(140),
`practitioner_no` varchar(140),
`date_discharge` date,
`discharge_time` time(6),
`date_admitted` date,
`admitted_time` time(6),
`patient_type_code` varchar(140),
`attendance_date` date,
`attendance_time` time(6),
`patient_file_no` text,
`patient_file` longtext,
`claim_file` longtext,
`clinical_notes` longtext,
`hms_tz_claim_appointment_list` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:58,389 WARNING database DDL Query made to DB:
create table `tabOriginal Delivery Note Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`hms_tz_is_out_of_stock` int(1) not null default 0,
`barcode` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`customer_item_code` varchar(140),
`description` longtext,
`brand` varchar(140),
`item_group` varchar(140),
`image` text,
`qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_qty` decimal(21,9) not null default 0,
`returned_qty` decimal(21,9) not null default 0,
`price_list_rate` decimal(21,9) not null default 0,
`base_price_list_rate` decimal(21,9) not null default 0,
`margin_type` varchar(140),
`margin_rate_or_amount` decimal(21,9) not null default 0,
`rate_with_margin` decimal(21,9) not null default 0,
`discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`base_rate_with_margin` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`base_rate` decimal(21,9) not null default 0,
`base_amount` decimal(21,9) not null default 0,
`pricing_rules` text,
`stock_uom_rate` decimal(21,9) not null default 0,
`is_free_item` int(1) not null default 0,
`grant_commission` int(1) not null default 0,
`net_rate` decimal(21,9) not null default 0,
`net_amount` decimal(21,9) not null default 0,
`item_tax_template` varchar(140),
`base_net_rate` decimal(21,9) not null default 0,
`base_net_amount` decimal(21,9) not null default 0,
`billed_amt` decimal(21,9) not null default 0,
`incoming_rate` decimal(21,9) not null default 0,
`weight_per_unit` decimal(21,9) not null default 0,
`total_weight` decimal(21,9) not null default 0,
`weight_uom` varchar(140),
`warehouse` varchar(140),
`target_warehouse` varchar(140),
`quality_inspection` varchar(140),
`against_sales_order` varchar(140),
`so_detail` varchar(140),
`against_sales_invoice` varchar(140),
`si_detail` varchar(140),
`batch_no` varchar(140),
`serial_no` text,
`actual_batch_qty` decimal(21,9) not null default 0,
`actual_qty` decimal(21,9) not null default 0,
`installed_qty` decimal(21,9) not null default 0,
`item_tax_rate` text,
`expense_account` varchar(140),
`allow_zero_valuation_rate` int(1) not null default 0,
`healthcare_service_unit` varchar(140),
`healthcare_practitioner` varchar(140),
`department` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`page_break` int(1) not null default 0,
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`original_item` varchar(140),
`original_stock_uom_qty` decimal(21,9) not null default 0,
`is_restricted` int(1) not null default 0,
`approval_number` varchar(140),
`approval_type` varchar(140),
`last_date_prescribed` date,
`last_qty_prescribed` decimal(21,9) not null default 0,
`recommended_qty` decimal(21,9) not null default 0,
index `item_code`(`item_code`),
index `so_detail`(`so_detail`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:58,505 WARNING database DDL Query made to DB:
create table `tabNHIF Custom Excluded Services` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`time_stamp` datetime(6),
`facilitycode` varchar(140),
`title` varchar(140),
`item` varchar(140),
`itemcode` varchar(140),
`excludedforproducts` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:58,653 WARNING database DDL Query made to DB:
create table `tabPrevious Lab Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_code` varchar(140),
`medical_code` varchar(140),
`override_subscription` int(1) not null default 0,
`prescribe` int(1) not null default 0,
`is_not_available_inhouse` int(1) not null default 0,
`lab_test_name` varchar(140),
`invoiced` int(1) not null default 0,
`lab_test` varchar(140),
`lab_test_comment` text,
`lab_test_created` int(1) not null default 0,
`department_hsu` varchar(140),
`intent` varchar(140),
`sequence` int(11) not null default 0,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`note` text,
`delivered_quantity` decimal(21,9) not null default 0,
`sales_invoice_number` varchar(140),
`reference_journal_entry` varchar(140),
`cancelled` int(1) not null default 0,
index `invoiced`(`invoiced`),
index `lab_test_created`(`lab_test_created`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:58,763 WARNING database DDL Query made to DB:
create table `tabHealthcare Package Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`healthcare_service_type` varchar(140),
`healthcare_service` varchar(140),
`price_list` varchar(140),
`actual_item_price` decimal(21,9) not null default 0,
`service_price` decimal(21,9) not null default 0,
`quantity` decimal(21,9) not null default 0,
`dosage` varchar(140),
`period` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:58,876 WARNING database DDL Query made to DB:
create table `tabNHIF Excluded Services` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`facilitycode` varchar(140),
`log_name` varchar(140),
`time_stamp` datetime(6),
`company` varchar(140),
`itemcode` varchar(140),
`schemeid` varchar(140),
`schemename` varchar(140),
`excludedforproducts` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:58,994 WARNING database DDL Query made to DB:
create table `tabNHIF Scheme` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`scheme_id` varchar(140) unique,
`scheme_name` varchar(140),
`healthcare_insurance_coverage_plan` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:59,119 WARNING database DDL Query made to DB:
create table `tabVC Insurance Rate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`coverage_plan` varchar(140),
`document_type` varchar(140),
`vc_rate` decimal(21,9) not null default 0,
`company_rate` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:59,220 WARNING database DDL Query made to DB:
create table `tabVC Excluded Service Rate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`healthcare_service` varchar(140),
`vc_rate` decimal(21,9) not null default 0,
`company_rate` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:59,327 WARNING database DDL Query made to DB:
create table `tabNHIF Physician Qualification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`id` varchar(140) unique,
`qualification` varchar(140),
`physicianqualificationid` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:59,460 WARNING database DDL Query made to DB:
create table `tabVisiting Comission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`valid_from` date,
`naming_series` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:59,576 WARNING database DDL Query made to DB:
create table `tabNHIF Update` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`datetime` datetime(6),
`current_log` varchar(140),
`previous_log` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:59,701 WARNING database DDL Query made to DB:
create table `tabPatient Discount Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_category` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`actual_price` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`amount_after_discount` decimal(21,9) not null default 0,
`sales_invoice` varchar(140),
`si_detail` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:59,804 WARNING database DDL Query made to DB:
create table `tabOrgan System` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`system_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:59,974 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare External Referrer` ADD COLUMN `referrer_name` varchar(140) unique, ADD COLUMN `facility_code` varchar(140)
2025-05-21 11:51:59,975 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare External Referrer` ADD UNIQUE INDEX IF NOT EXISTS referrer_name (`referrer_name`)
2025-05-21 11:52:00,135 WARNING database DDL Query made to DB:
create table `tabPatient Discount Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`customer` varchar(140),
`inpatient_record` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`insurance_subscription` varchar(140),
`insurance_coverage_plan` varchar(140),
`insurance_company` varchar(140),
`payment_type` varchar(140),
`apply_discount_on` varchar(140),
`item_category` varchar(140),
`appointment` varchar(140),
`sales_invoice` varchar(140),
`discount_criteria` varchar(140),
`discount_percent` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`total_actual_amount` decimal(21,9) not null default 0,
`total_discounted_amount` decimal(21,9) not null default 0,
`total_amount_after_discount` decimal(21,9) not null default 0,
`requested_by` varchar(140),
`approved_by` varchar(140),
`naming_series` varchar(140),
`amended_from` varchar(140),
`discount_reason` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:00,258 WARNING database DDL Query made to DB:
create table `tabHealthcare Package Consultation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`consultation_item` varchar(140),
`price_list` varchar(140),
`actual_item_price` decimal(21,9) not null default 0,
`service_price` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:00,360 WARNING database DDL Query made to DB:
create table `tabPrevious Diet Recommendation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`diet_plan` varchar(140),
`medical_code` varchar(140),
`occurance` int(11) not null default 0,
`comments` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:00,497 WARNING database DDL Query made to DB:
create table `tabNHIF Price Package` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`facilitycode` varchar(140),
`log_name` varchar(140),
`time_stamp` datetime(6),
`company` varchar(140),
`itemcode` varchar(140),
`pricecode` varchar(140),
`levelpricecode` varchar(140),
`olditemcode` varchar(140),
`itemtypeid` varchar(140),
`itemname` text,
`strength` varchar(140),
`dosage` varchar(140),
`packageid` varchar(140),
`schemeid` varchar(140),
`facilitylevelcode` varchar(140),
`unitprice` varchar(140),
`isrestricted` varchar(140),
`maximumquantity` varchar(140),
`availableinlevels` varchar(140),
`practitionerqualifications` varchar(140),
`isactive` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:00,638 WARNING database DDL Query made to DB:
create table `tabPractitioner Availability Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`practitioner_availability` varchar(140),
`availability_type` varchar(140),
`availability` varchar(140),
`practitioner` varchar(140),
`healthcare_practitioner_name` varchar(140),
`from_date` datetime(6),
`from_time` time(6),
`to_date` datetime(6),
`to_time` time(6),
`repeat_this_event` int(1) not null default 0,
`present` int(1) not null default 0,
`appointment_type` varchar(140),
`duration` int(11) not null default 0,
`service_unit` varchar(140),
`total_service_unit_capacity` int(11) not null default 0,
`color` varchar(140),
`out_patient_consulting_charge_item` varchar(140),
`op_consulting_charge` decimal(21,9) not null default 0,
`inpatient_visit_charge_item` varchar(140),
`inpatient_visit_charge` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:00,776 WARNING database DDL Query made to DB:
create table `tabNHIF Folio Counter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`claim_year` int(11) not null default 0,
`folio_no` int(11) not null default 0,
`posting_date` datetime(6),
`claim_month` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:00,924 WARNING database DDL Query made to DB:
create table `tabNHIF Tracking Claim Change` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`quantity` int(11) not null default 0,
`claim_month` int(11) not null default 0,
`claim_year` int(11) not null default 0,
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`status` varchar(140),
`previous_amount` decimal(21,9) not null default 0,
`current_amount` decimal(21,9) not null default 0,
`amount_changed` decimal(21,9) not null default 0,
`nhif_patient_claim` varchar(140),
`lrpmt_return` varchar(140),
`patient_appointment` varchar(140),
`medication_change_request` varchar(140),
`patient_encounter` varchar(140),
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`user_email` varchar(140),
`edited_by` varchar(140),
`comment` text,
`naming_series` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:01,054 WARNING database DDL Query made to DB:
create table `tabNHIF Patient Claim Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140),
`item_name` varchar(140),
`item_code` varchar(140),
`item_quantity` int(11) not null default 0,
`unit_price` decimal(21,9) not null default 0,
`amount_claimed` decimal(21,9) not null default 0,
`created_by` varchar(140),
`item_crt_by` varchar(140) default 'None',
`status` varchar(140),
`patient_encounter` text,
`ref_docname` text,
`approval_ref_no` varchar(140),
`folio_item_id` varchar(140),
`folio_id` varchar(140),
`date_created` date,
`claim_status` varchar(140),
`claim_closed` int(1) not null default 0,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`claim_status_modification_notes` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:01,183 WARNING database DDL Query made to DB:
create table `tabOriginal NHIF Patient Claim Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140),
`item_name` varchar(140),
`item_code` varchar(140),
`item_quantity` int(11) not null default 0,
`unit_price` decimal(21,9) not null default 0,
`amount_claimed` decimal(21,9) not null default 0,
`created_by` varchar(140),
`item_crt_by` varchar(140) default 'None',
`status` varchar(140),
`patient_encounter` text,
`ref_docname` text,
`approval_ref_no` varchar(140),
`folio_item_id` varchar(140),
`folio_id` varchar(140),
`date_created` date,
`claim_status` varchar(140),
`claim_closed` int(1) not null default 0,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`claim_status_modification_notes` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:01,305 WARNING database DDL Query made to DB:
create table `tabNHIF Response Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`timestamp` datetime(6),
`request_type` varchar(140),
`request_url` varchar(1000),
`request_header` text,
`user_id` varchar(140),
`status_code` varchar(140),
`request_body` longtext,
`response_data` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:01,457 WARNING database DDL Query made to DB:
create table `tabHealthcare Referral` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`appointment` varchar(140),
`patient_name` varchar(140),
`gender` varchar(140),
`referral_status` varchar(140),
`card_no` varchar(140),
`authorization_number` varchar(140),
`posting_date` datetime(6),
`source_facility` varchar(140),
`source_facility_code` varchar(140),
`referrer_facility` varchar(140),
`referrer_facility_code` varchar(140),
`practitioner` varchar(140),
`mobile_no` varchar(140),
`nhif_physician_qualification` varchar(140),
`reason_for_referral` text,
`referring_diagnosis` text,
`referral_no` varchar(140),
`referralid` varchar(140),
`referringdate` date,
`naming_series` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:01,619 WARNING database DDL Query made to DB:
create table `tabNHIF Claim Reconciliation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`claim_year` varchar(140),
`claim_month` int(11) not null default 0,
`status` varchar(140),
`posting_date` date,
`number_of_submitted_claims` int(11) not null default 0,
`total_amount_claimed` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `company`(`company`),
index `claim_year`(`claim_year`),
index `claim_month`(`claim_month`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:01,763 WARNING database DDL Query made to DB:
create table `tabPrevious Therapy Plan Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`therapy_type` varchar(140),
`medical_code` varchar(140),
`override_subscription` int(1) not null default 0,
`prescribe` int(1) not null default 0,
`is_not_available_inhouse` int(1) not null default 0,
`no_of_sessions` int(11) not null default 0,
`sessions_completed` int(11) not null default 0,
`department_hsu` varchar(140),
`comment` text,
`intent` varchar(140),
`sequence` int(11) not null default 0,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`body_part` varchar(140),
`note` text,
`delivered_quantity` decimal(21,9) not null default 1.0,
`sales_invoice_number` varchar(140),
`reference_journal_entry` varchar(140),
`cancelled` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:01,913 WARNING database DDL Query made to DB:
create table `tabPrevious Procedure Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`procedure` varchar(140),
`medical_code` varchar(140),
`override_subscription` int(1) not null default 0,
`prescribe` int(1) not null default 0,
`is_not_available_inhouse` int(1) not null default 0,
`procedure_name` varchar(140),
`department` varchar(140),
`practitioner` varchar(140),
`date` date,
`comments` varchar(140),
`appointment_booked` int(1) not null default 0,
`procedure_created` int(1) not null default 0,
`invoiced` int(1) not null default 0,
`clinical_procedure` varchar(140),
`department_hsu` varchar(140),
`override_insurance_subscription` int(1) not null default 0,
`hso_payment_method` varchar(140),
`intent` varchar(140),
`sequence` int(11) not null default 0,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`body_part` varchar(140),
`note` text,
`delivered_quantity` decimal(21,9) not null default 0,
`sales_invoice_number` varchar(140),
`reference_journal_entry` varchar(140),
`cancelled` int(1) not null default 0,
index `appointment_booked`(`appointment_booked`),
index `procedure_created`(`procedure_created`),
index `invoiced`(`invoiced`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:02,048 WARNING database DDL Query made to DB:
create table `tabChronic Medications` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`drug_code` varchar(140),
`drug_name` varchar(140),
`dosage` varchar(140),
`period` varchar(140),
`dosage_form` varchar(140),
`comment` text,
`usage_interval` int(1) not null default 0,
`interval` int(11) not null default 0,
`interval_uom` varchar(140),
`update_schedule` int(1) not null default 1,
`intent` varchar(140),
`quantity` int(11) not null default 0,
`sequence` int(11) not null default 0,
`expected_date` date,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`occurrence` datetime(6),
`occurence_period` decimal(21,9),
`note` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:02,192 WARNING database DDL Query made to DB:
create table `tabPrevious Drug Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`drug_code` varchar(140),
`medical_code` varchar(140),
`override_subscription` int(1) not null default 0,
`prescribe` int(1) not null default 0,
`is_not_available_inhouse` int(1) not null default 0,
`drug_name` varchar(140),
`dosage` varchar(140),
`period` varchar(140),
`dosage_form` varchar(140),
`healthcare_service_unit` varchar(140),
`is_restricted` int(1) not null default 0,
`comment` text,
`usage_interval` int(1) not null default 0,
`interval` int(11) not null default 0,
`interval_uom` varchar(140),
`update_schedule` int(1) not null default 1,
`intent` varchar(140),
`quantity` int(11) not null default 0,
`sequence` int(11) not null default 0,
`expected_date` date,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`occurrence` datetime(6),
`occurence_period` decimal(21,9),
`note` text,
`drug_prescription_created` int(1) not null default 0,
`delivered_quantity` decimal(21,9) not null default 0,
`sales_invoice_number` varchar(140),
`reference_journal_entry` varchar(140),
`cancelled` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:02,322 WARNING database DDL Query made to DB:
create table `tabMedication Change Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`appointment` varchar(140),
`hms_tz_comment` text,
`company` varchar(140),
`patient_encounter` varchar(140),
`delivery_note` varchar(140),
`sales_order` varchar(140),
`healthcare_practitioner` varchar(140),
`practitioner_name` varchar(140),
`medical_department` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `delivery_note`(`delivery_note`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:02,448 WARNING database DDL Query made to DB:
create table `tabHealthcare Package Order Consultation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`consultation_item` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`department` varchar(140),
`consultation_fee` decimal(21,9) not null default 0,
`appointment` varchar(140),
`encounter` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:02,599 WARNING database DDL Query made to DB:
create table `tabStaging NHIF Price Package` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140),
`package_item_id` varchar(140),
`facilitycode` varchar(140),
`itemcode` varchar(140),
`pricecode` varchar(140),
`levelpricecode` varchar(140),
`olditemcode` varchar(140),
`itemtypeid` varchar(140),
`itemname` varchar(140),
`schemename` varchar(140),
`strength` varchar(140),
`dosage` varchar(140),
`packageid` varchar(140),
`schemeid` varchar(140),
`facilitylevelcode` varchar(140),
`unitprice` varchar(140),
`isrestricted` varchar(140),
`maximumquantity` varchar(140),
`availableinlevels` varchar(140),
`practitionerqualifications` varchar(140),
`isactive` varchar(140),
`record` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:02,710 WARNING database DDL Query made to DB:
create table `tabStaging NHIF Excluded Services` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140),
`facilitycode` varchar(140),
`itemcode` varchar(140),
`schemeid` varchar(140),
`schemename` varchar(140),
`excludedforproducts` text,
`record` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:02,829 WARNING database DDL Query made to DB:
create table `tabNHIF Patient Claim Disease` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`diagnosis_type` varchar(140),
`status` varchar(140),
`medical_code` varchar(140),
`disease_code` varchar(140),
`description` varchar(140),
`patient_encounter` varchar(140),
`codification_table` varchar(140),
`folio_id` varchar(140),
`folio_disease_id` varchar(140),
`created_by` varchar(140),
`item_crt_by` varchar(140),
`date_created` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:02,964 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Machine Message` ADD COLUMN `sample_collection` varchar(140), ADD COLUMN `machine_id` varchar(140)
2025-05-21 11:52:03,102 WARNING database DDL Query made to DB:
create table `tabHealthcare Package Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`healthcare_package` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`patient` varchar(140),
`patient_name` varchar(140),
`non_consultation_appointment` varchar(140),
`non_consultation_encounter` varchar(140),
`payment_type` varchar(140),
`mode_of_payment` varchar(140),
`insurance_subscription` varchar(140),
`authorization_number` varchar(140),
`total_price` decimal(21,9) not null default 0,
`paid` int(1) not null default 0,
`sales_invoice` varchar(140),
`amended_from` varchar(140),
`naming_series` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:03,234 WARNING database DDL Query made to DB:
create table `tabVC LRPMT Submitter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user_field` varchar(140),
`wtax` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:32,517 WARNING database DDL Query made to DB:
ALTER TABLE `tabVital Signs` MODIFY `height` decimal(21,9) not null default 0, MODIFY `weight` decimal(21,9) not null default 0
2025-05-21 11:52:32,684 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Sample` ADD COLUMN `sample_type` varchar(140), ADD COLUMN `container_closure_color` varchar(140)
2025-05-21 11:52:32,947 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` ADD COLUMN `practitioner_type` varchar(140) default 'Internal', ADD COLUMN `google_calendar` varchar(140), ADD COLUMN `enable_free_follow_ups` int(1) not null default 0, ADD COLUMN `max_visits` int(11) not null default 0, ADD COLUMN `valid_days` int(11) not null default 0, ADD COLUMN `practitioner_primary_contact` varchar(140), ADD COLUMN `mobile_no` varchar(140), ADD COLUMN `email_id` varchar(140), ADD COLUMN `practitioner_primary_address` varchar(140), ADD COLUMN `primary_address` text
2025-05-21 11:52:32,969 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `op_consulting_charge` decimal(21,9) not null default 0, MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0
2025-05-21 11:52:33,326 WARNING database DDL Query made to DB:
create table `tabService Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`naming_series` varchar(140),
`order_date` date,
`order_time` time(6),
`status` varchar(140),
`company` varchar(140),
`expected_date` date,
`patient` varchar(140),
`patient_name` varchar(140),
`patient_gender` varchar(140),
`patient_birth_date` date,
`patient_age_data` varchar(140),
`patient_age` int(11) not null default 0,
`patient_blood_group` varchar(140),
`patient_email` varchar(140),
`patient_mobile` varchar(140),
`inpatient_record` varchar(140),
`inpatient_status` varchar(140),
`practitioner` varchar(140),
`practitioner_email` varchar(140),
`medical_department` varchar(140),
`referred_to_practitioner` varchar(140),
`source_doc` varchar(140),
`order_group` varchar(140),
`sequence` int(11) not null default 0,
`staff_role` varchar(140),
`item_code` varchar(140),
`patient_care_type` varchar(140),
`intent` varchar(140),
`priority` varchar(140),
`quantity` int(11) not null default 1,
`dosage_form` varchar(140),
`as_needed` int(1) not null default 0,
`dosage` varchar(140),
`period` varchar(140),
`occurrence_date` date,
`occurrence_time` time(6),
`interval` decimal(21,9),
`healthcare_service_unit_type` varchar(140),
`order_description` text,
`patient_instructions` text,
`order_reference_doctype` varchar(140),
`order_reference_name` varchar(140),
`source` varchar(140),
`referring_practitioner` varchar(140),
`reason_reference_doctype` varchar(140),
`reason_reference` varchar(140),
`order_group_doctype` varchar(140) default 'Patient Encounter',
`amended_from` varchar(140),
`template_dt` varchar(140),
`template_dn` varchar(140),
`sample_collection_required` int(1) not null default 0,
`qty_invoiced` decimal(21,9) not null default 0,
`billing_status` varchar(140) default 'Pending',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `template_dt`(`template_dt`),
index `template_dn`(`template_dn`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:33,514 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Schedule Time Slot` ADD COLUMN `duration` decimal(21,9) not null default 0, ADD COLUMN `maximum_appointments` int(11) not null default 0
2025-05-21 11:52:33,631 WARNING database DDL Query made to DB:
create table `tabCode Value Set` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`code_system` varchar(140),
`system_uri` varchar(140) unique,
`value_set` varchar(140) unique,
`description` text,
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:33,781 WARNING database DDL Query made to DB:
create table `tabNursing Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140),
`date` date,
`user` varchar(140),
`company` varchar(140),
`service_unit` varchar(140),
`medical_department` varchar(140),
`status` varchar(140) default 'Draft',
`activity` varchar(140),
`mandatory` int(1) not null default 0,
`description` text,
`patient` varchar(140),
`patient_name` varchar(140),
`age` varchar(140),
`gender` varchar(140),
`inpatient_record` varchar(140),
`inpatient_status` varchar(140),
`requested_start_time` datetime(6),
`requested_end_time` datetime(6),
`duration` decimal(21,9),
`task_start_time` datetime(6),
`task_end_time` datetime(6),
`task_duration` decimal(21,9),
`reference_doctype` varchar(140),
`amended_from` varchar(140),
`reference_name` varchar(140),
`task_doctype` varchar(140),
`task_document_name` varchar(140),
`notes` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:34,019 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` DROP INDEX `healthcare_service_unit_name`
2025-05-21 11:52:34,114 WARNING database DDL Query made to DB:
alter table `tabHealthcare Service Unit`
					add unique `unique_service_unit_company`(healthcare_service_unit_name, company)
2025-05-21 11:52:34,177 WARNING database DDL Query made to DB:
create table `tabMedication Linked Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item` varchar(140),
`item_group` varchar(140),
`stock_uom` varchar(140),
`brand` varchar(140),
`manufacturer` varchar(140),
`description` text,
`is_billable` int(1) not null default 1,
`rate` decimal(21,9) not null default 0,
`change_in_item` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:34,330 WARNING database DDL Query made to DB:
create table `tabDischarge Summary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140) default 'HLC-DS-.YYYY.-',
`inpatient_record` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`gender` varchar(140),
`date_of_birth` date,
`discharge_practitioner` varchar(140),
`posting_date` date,
`company` varchar(140),
`status` varchar(140),
`followup_date` date,
`review_date` date,
`admission_encounter` varchar(140),
`admission_practitioner` varchar(140),
`medical_department` varchar(140),
`scheduled_date` date,
`admitted_datetime` datetime(6),
`primary_practitioner` varchar(140),
`secondary_practitioner` varchar(140),
`physical_examination` longtext,
`treatment_done` longtext,
`advice_on_discharge` longtext,
`diet_adviced` longtext,
`current_issues` longtext,
`instructions` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:34,456 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Medication Order Entry` MODIFY `dosage` decimal(21,9) not null default 0
2025-05-21 11:52:34,738 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` ADD COLUMN `service_request` varchar(140)
2025-05-21 11:52:34,758 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-05-21 11:52:34,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` ADD INDEX `service_request_index`(`service_request`)
2025-05-21 11:52:35,071 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` ADD COLUMN `admission_nursing_checklist_template` varchar(140), ADD COLUMN `discharge_nursing_checklist_template` varchar(140), ADD COLUMN `currency` varchar(140), ADD COLUMN `price_list` varchar(140), ADD COLUMN `total` decimal(21,9) not null default 0, ADD COLUMN `paid_amount` decimal(21,9) not null default 0
2025-05-21 11:52:35,344 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` ADD COLUMN `service_request` varchar(140), ADD COLUMN `patient_care_type` varchar(140)
2025-05-21 11:52:35,466 WARNING database DDL Query made to DB:
create table `tabDiagnostic Report` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`gender` varchar(140),
`age` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`company` varchar(140),
`status` varchar(140),
`naming_series` varchar(140),
`ref_doctype` varchar(140),
`docname` varchar(140),
`reference_posting_date` date,
`sample_collection` varchar(140),
`amended_from` varchar(140),
`title` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `sample_collection`(`sample_collection`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:35,630 WARNING database DDL Query made to DB:
ALTER TABLE `tabFee Validity` ADD COLUMN `medical_department` varchar(140), ADD COLUMN `patient_appointment` varchar(140), ADD COLUMN `sales_invoice_ref` varchar(140)
2025-05-21 11:52:35,848 WARNING database DDL Query made to DB:
create table `tabCode Value` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`code_system` varchar(140),
`system_uri` varchar(140),
`experimental` int(1) not null default 0,
`immutable` int(1) not null default 0,
`complete` int(1) not null default 0,
`code_value` varchar(140),
`value_set` varchar(140),
`display` text,
`status` varchar(140),
`version` varchar(140),
`level` int(10) not null default 0,
`definition` text,
`official_url` varchar(140),
`canonical_mapping` varchar(140),
`custom` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `code_system`(`code_system`),
index `system_uri`(`system_uri`),
index `code_value`(`code_value`),
index `version`(`version`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:36,255 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Template` ADD COLUMN `link_existing_item` int(1) not null default 0
2025-05-21 11:52:36,276 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Template` MODIFY `total_amount` decimal(21,9) not null default 0
2025-05-21 11:52:36,449 WARNING database DDL Query made to DB:
create table `tabObservation Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`observation` varchar(140) unique,
`observation_category` varchar(140),
`preferred_display_name` varchar(140),
`abbr` varchar(140),
`has_component` int(1) not null default 0,
`medical_department` varchar(140),
`description` longtext,
`method` varchar(140),
`method_value` varchar(140),
`service_unit` varchar(140),
`result_template` varchar(140),
`interpretation_template` varchar(140),
`permitted_data_type` varchar(140),
`permitted_unit` varchar(140),
`options` text,
`template` varchar(140),
`sample_collection_required` int(1) not null default 0,
`sample` varchar(140),
`sample_qty` decimal(21,9) not null default 0,
`uom` varchar(140),
`sample_type` varchar(140),
`container_closure_color` varchar(140),
`sample_details` text,
`is_billable` int(1) not null default 0,
`link_existing_item` int(1) not null default 0,
`item` varchar(140),
`item_code` varchar(140),
`item_group` varchar(140),
`rate` decimal(21,9) not null default 0,
`change_in_item` int(1) not null default 0,
`patient_care_type` varchar(140),
`staff_role` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `is_billable`(`is_billable`),
index `item`(`item`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:36,645 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` ADD COLUMN `pre_op_nursing_checklist_template` varchar(140), ADD COLUMN `post_op_nursing_checklist_template` varchar(140), ADD COLUMN `link_existing_item` int(1) not null default 0
2025-05-21 11:52:36,668 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `sample_qty` decimal(21,9) not null default 0
2025-05-21 11:52:36,818 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Medication Order` MODIFY `total_orders` decimal(21,9) not null default 0, MODIFY `completed_orders` decimal(21,9) not null default 0
2025-05-21 11:52:36,994 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` ADD COLUMN `is_combination` int(1) not null default 0, ADD COLUMN `price_list` varchar(140), ADD COLUMN `dosage_form` varchar(140)
2025-05-21 11:52:37,023 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `strength` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS generic_name (`generic_name`)
2025-05-21 11:52:37,144 WARNING database DDL Query made to DB:
create table `tabInpatient Record Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`quantity` decimal(21,9) not null default 0,
`uom` varchar(140),
`invoiced` int(1) not null default 0,
`conversion_factor` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`stock_entry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:37,254 WARNING database DDL Query made to DB:
create table `tabObservation Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`observation_template` varchar(140),
`abbr` varchar(140),
`condition` longtext,
`based_on_formula` int(1) not null default 0,
`formula` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:37,475 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` ADD COLUMN `nursing_checklist_template` varchar(140)
2025-05-21 11:52:37,497 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-05-21 11:52:37,775 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` ADD COLUMN `appointment_for` varchar(140), ADD COLUMN `reference_doctype` varchar(140), ADD COLUMN `reference_docname` varchar(140), ADD COLUMN `service_request` varchar(140), ADD COLUMN `add_video_conferencing` int(1) not null default 0, ADD COLUMN `event` varchar(140), ADD COLUMN `google_meet_link` varchar(140), ADD COLUMN `position_in_queue` int(11) not null default 0, ADD COLUMN `appointment_based_on_check_in` int(1) not null default 0
2025-05-21 11:52:37,797 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `paid_amount` decimal(21,9) not null default 0
2025-05-21 11:52:37,827 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` ADD INDEX `service_request_index`(`service_request`)
2025-05-21 11:52:38,178 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` ADD COLUMN `medication` varchar(140), ADD COLUMN `strength` decimal(21,9) not null default 0, ADD COLUMN `strength_uom` varchar(140), ADD COLUMN `dosage_by_interval` int(1) not null default 0, ADD COLUMN `number_of_repeats_allowed` decimal(21,9) not null default 0, ADD COLUMN `medication_request` varchar(140)
2025-05-21 11:52:38,295 WARNING database DDL Query made to DB:
create table `tabABDM Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`gateway_name` varchar(140),
`default` int(1) not null default 0,
`company` varchar(140),
`auth_base_url` varchar(140),
`client_id` varchar(140),
`client_secret` varchar(140),
`health_id_base_url` varchar(140),
`consent_base_url` varchar(140),
`patient_aadhaar_consent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:38,596 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test` ADD COLUMN `date` date, ADD COLUMN `time` time(6), ADD COLUMN `service_unit` varchar(140), ADD COLUMN `descriptive_result` longtext, ADD COLUMN `service_request` varchar(140), ADD COLUMN `imaging_toggle` int(1) not null default 0
2025-05-21 11:52:38,628 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test` ADD INDEX `service_request_index`(`service_request`)
2025-05-21 11:52:38,815 WARNING database DDL Query made to DB:
ALTER TABLE `tabSample Collection` ADD COLUMN `referring_practitioner` varchar(140), ADD COLUMN `status` varchar(140), ADD COLUMN `collection_point` varchar(140), ADD COLUMN `service_request` varchar(140), ADD COLUMN `reference_doc` varchar(140), ADD COLUMN `reference_name` varchar(140)
2025-05-21 11:52:38,957 WARNING database DDL Query made to DB:
create table `tabCode System` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`code_system` varchar(140) unique,
`uri` varchar(140),
`description` text,
`status` varchar(140),
`version` varchar(140),
`is_fhir_defined` int(1) not null default 1,
`oid` varchar(140) unique,
`experimental` int(1) not null default 1,
`immutable` int(1) not null default 0,
`complete` int(1) not null default 0,
`custom` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `disabled`(`disabled`),
index `version`(`version`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:39,161 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure` ADD COLUMN `service_request` varchar(140)
2025-05-21 11:52:39,183 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure` MODIFY `consumable_total_amount` decimal(21,9) not null default 0
2025-05-21 11:52:39,311 WARNING database DDL Query made to DB:
create table `tabMedication Ingredient` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication` varchar(140),
`strength` decimal(21,9) not null default 0,
`strength_uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:39,484 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` ADD COLUMN `minimum_billable_qty` int(11) not null default 0, ADD COLUMN `is_ot` int(1) not null default 0, ADD COLUMN `medical_department` varchar(140)
2025-05-21 11:52:39,506 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-05-21 11:52:39,621 WARNING database DDL Query made to DB:
create table `tabSpecimen` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`specimen_type` varchar(140),
`received_time` datetime(6),
`status` varchar(140),
`barcode` longtext,
`patient` varchar(140),
`patient_name` varchar(140),
`patient_age` varchar(140),
`patient_gender` varchar(140),
`note` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:39,983 WARNING database DDL Query made to DB:
ALTER TABLE `tabTreatment Plan Template Item` ADD COLUMN `amount` decimal(21,9) not null default 0, ADD COLUMN `service_request` varchar(140), ADD COLUMN `drug_code` varchar(140), ADD COLUMN `drug_name` varchar(140), ADD COLUMN `strength` decimal(21,9) not null default 0, ADD COLUMN `strength_uom` varchar(140), ADD COLUMN `dosage_form` varchar(140), ADD COLUMN `dosage_by_interval` int(1) not null default 0, ADD COLUMN `dosage` varchar(140), ADD COLUMN `interval` int(11) not null default 0, ADD COLUMN `interval_uom` varchar(140), ADD COLUMN `period` varchar(140), ADD COLUMN `number_of_repeats_allowed` decimal(21,9) not null default 0
2025-05-21 11:52:40,159 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` ADD COLUMN `interval` decimal(21,9), ADD COLUMN `service_request` varchar(140), ADD COLUMN `patient_care_type` varchar(140)
2025-05-21 11:52:40,385 WARNING database DDL Query made to DB:
ALTER TABLE `tabDosage Strength` MODIFY `strength` decimal(21,9) not null default 0
2025-05-21 11:52:40,612 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Type Service Item` ADD COLUMN `dt` varchar(140), ADD COLUMN `dn` varchar(140)
2025-05-21 11:52:40,633 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Type Service Item` MODIFY `op_consulting_charge` decimal(21,9) not null default 0, MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0
2025-05-21 11:52:40,777 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Type` ADD COLUMN `allow_booking_for` varchar(140) default 'Practitioner'
2025-05-21 11:52:40,906 WARNING database DDL Query made to DB:
create table `tabClinical Note` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`gender` varchar(140),
`blood_group` varchar(140),
`clinical_note_type` varchar(140),
`terms_and_conditions` varchar(140),
`posting_date` datetime(6),
`practitioner` varchar(140),
`user` varchar(140),
`note` longtext,
`reference_doc` varchar(140),
`reference_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:41,223 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD COLUMN `nursing_checklist_template` varchar(140), ADD COLUMN `link_existing_item` int(1) not null default 0, ADD COLUMN `descriptive_result` longtext
2025-05-21 11:52:41,244 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_name (`lab_test_name`)
2025-05-21 11:52:41,273 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name`, DROP INDEX `lab_test_code`
2025-05-21 11:52:41,429 WARNING database DDL Query made to DB:
ALTER TABLE `tabTreatment Plan Template` ADD COLUMN `is_inpatient` int(1) not null default 0, ADD COLUMN `treatment_counselling_required_for_ip` int(1) not null default 0, ADD COLUMN `healthcare_service_unit_type` varchar(140), ADD COLUMN `expected_length_of_stay` int(11) not null default 0
2025-05-21 11:52:41,851 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-05-21 11:52:42,088 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` ADD COLUMN `status` varchar(140), ADD COLUMN `google_meet_link` varchar(140), ADD COLUMN `submit_orders_on_save` int(1) not null default 0
2025-05-21 11:52:42,208 WARNING database DDL Query made to DB:
create sequence if not exists observation_sample_collection_id_seq nocache nocycle
2025-05-21 11:52:42,227 WARNING database DDL Query made to DB:
create table `tabObservation Sample Collection` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`observation_template` varchar(140),
`has_component` int(1) not null default 0,
`sample` varchar(140) default 'Urine',
`sample_type` varchar(140),
`uom` varchar(140),
`status` varchar(140),
`container_closure_color` varchar(140),
`sample_qty` decimal(21,9) not null default 0,
`medical_department` varchar(140),
`collection_date_time` datetime(6),
`collection_point` varchar(140),
`collected_user` varchar(140),
`collected_by` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`reference_child` varchar(140),
`service_request` varchar(140),
`specimen` varchar(140),
`component_observation_parent` varchar(140),
`component_observations` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:42,654 WARNING database DDL Query made to DB:
create table `tabObservation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`observation_template` varchar(140),
`observation_category` varchar(140),
`company` varchar(140),
`posting_date` date,
`status` varchar(140) default 'Registered',
`medical_department` varchar(140),
`amended_from` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`age` varchar(140),
`gender` varchar(140),
`healthcare_practitioner` varchar(140),
`practitioner_name` varchar(140),
`has_component` int(1) not null default 0,
`preferred_display_name` varchar(140),
`sample_collection_required` int(1) not null default 0,
`permitted_unit` varchar(140),
`sample` varchar(140),
`sample_type` varchar(140),
`permitted_data_type` varchar(140),
`method` varchar(140),
`specimen` varchar(140),
`sample_collection_time` datetime(6),
`sample_status` varchar(140),
`result_template` varchar(140),
`result_attach` text,
`result_boolean` varchar(140),
`result_data` varchar(140),
`result_text` longtext,
`result_float` decimal(21,9) not null default 0,
`result_select` varchar(140),
`result_datetime` datetime(6),
`result_time` datetime(6),
`result_period_from` datetime(6),
`result_period_to` datetime(6),
`options` text,
`time_of_result` datetime(6),
`time_of_approval` datetime(6),
`interpretation_template` varchar(140),
`result_interpretation` longtext,
`observation_method` varchar(140),
`reference` text,
`note` longtext,
`description` longtext,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`sales_invoice` varchar(140),
`sales_invoice_status` varchar(140),
`sales_invoice_item` varchar(140),
`service_request` varchar(140),
`disapproval_reason` text,
`parent_observation` varchar(140),
`observation_idx` int(11) not null default 0,
`days` int(11) not null default 0,
`invoiced` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:43,034 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Template Detail` ADD COLUMN `interval` decimal(21,9)
2025-05-21 11:52:43,057 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Template Detail` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-05-21 11:52:43,161 WARNING database DDL Query made to DB:
create table `tabService Request Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`category` varchar(140) unique,
`patient_care_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient_care_type`(`patient_care_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:43,347 WARNING database DDL Query made to DB:
create table `tabTreatment Counselling` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`customer` varchar(140),
`company` varchar(140),
`status` varchar(140),
`gender` varchar(140),
`patient_age` varchar(140),
`treatment_plan_template` varchar(140),
`price_list` varchar(140),
`amount` decimal(21,9) not null default 0,
`outstanding_amount` decimal(21,9) not null default 0,
`paid_amount` decimal(21,9) not null default 0,
`encounter_status` varchar(140),
`medical_department` varchar(140),
`primary_practitioner` varchar(140),
`secondary_practitioner` varchar(140),
`admission_nursing_checklist_template` varchar(140),
`admission_instruction` text,
`admission_ordered_for` date,
`admission_service_unit_type` varchar(140),
`expected_length_of_stay` int(11) not null default 0,
`admission_encounter` varchar(140),
`referring_practitioner` varchar(140),
`inpatient_record` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:43,480 WARNING database DDL Query made to DB:
create table `tabClinical Note Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`clinical_note_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:43,703 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Care Type` ADD COLUMN `description` text
2025-05-21 11:52:43,929 WARNING database DDL Query made to DB:
create table `tabService Request Reason` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_request_reason` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:44,185 WARNING database DDL Query made to DB:
create table `tabMedication Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`naming_series` varchar(140),
`medication` varchar(140),
`medication_item` varchar(140),
`order_date` date,
`order_time` time(6),
`expected_date` date,
`company` varchar(140),
`status` varchar(140) default 'draft-Medication Request Status',
`patient` varchar(140),
`patient_name` varchar(140),
`patient_gender` varchar(140),
`patient_birth_date` date,
`patient_age_data` varchar(140),
`patient_age` int(11) not null default 0,
`patient_blood_group` varchar(140),
`patient_email` varchar(140),
`patient_mobile` varchar(140),
`inpatient_record` varchar(140),
`inpatient_status` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`practitioner_email` varchar(140),
`medical_department` varchar(140),
`referred_to_practitioner` varchar(140),
`reason_reference_doctype` varchar(140),
`reason_reference` varchar(140),
`order_group` varchar(140),
`sequence` int(11) not null default 0,
`staff_role` varchar(140),
`item_code` varchar(140),
`intent` varchar(140),
`priority` varchar(140),
`quantity` int(11) not null default 1,
`dosage_form` varchar(140),
`dosage` varchar(140),
`number_of_repeats_allowed` decimal(21,9) not null default 0,
`order_description` text,
`period` varchar(140),
`occurrence_time` time(6),
`total_dispensable_quantity` decimal(21,9) not null default 0,
`billing_status` varchar(140) default 'Pending',
`qty_invoiced` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `medication`(`medication`),
index `status`(`status`),
index `patient`(`patient`),
index `patient_email`(`patient_email`),
index `patient_mobile`(`patient_mobile`),
index `inpatient_record`(`inpatient_record`),
index `practitioner`(`practitioner`),
index `staff_role`(`staff_role`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:44,331 WARNING database DDL Query made to DB:
ALTER TABLE `tabNormal Test Template` MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-05-21 11:52:44,761 WARNING database DDL Query made to DB:
ALTER TABLE `tabPractitioner Schedule` ADD COLUMN `allow_video_conferencing` int(1) not null default 0
2025-05-21 11:52:45,039 WARNING database DDL Query made to DB:
ALTER TABLE `tabCodification Table` ADD COLUMN `code_system` varchar(140), ADD COLUMN `system` varchar(140), ADD COLUMN `is_fhir_defined` int(1) not null default 0, ADD COLUMN `oid` varchar(140), ADD COLUMN `code_value` varchar(140), ADD COLUMN `display` varchar(140), ADD COLUMN `definition` text
2025-05-21 11:52:45,154 WARNING database DDL Query made to DB:
create table `tabSample Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sample_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:45,273 WARNING database DDL Query made to DB:
create table `tabHealthcare Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`activity` varchar(140) unique,
`description` text,
`activity_duration` decimal(21,9),
`role` varchar(140),
`task_doctype` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:45,809 WARNING database DDL Query made to DB:
create table `tabService Unit Type Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`is_stock_item` int(1) not null default 0,
`billing_type` varchar(140),
`charge` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:45,935 WARNING database DDL Query made to DB:
create table `tabNursing Checklist Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`title` varchar(140) unique,
`department` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:46,200 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-05-21 11:52:46,386 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Group Template` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `lab_test_rate` decimal(21,9) not null default 0
2025-05-21 11:52:46,582 WARNING database DDL Query made to DB:
create table `tabABDM Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`request_name` varchar(140),
`request_date` datetime(6),
`status` varchar(140),
`url` varchar(140),
`request` longtext,
`traceback` longtext,
`response` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:47,290 WARNING database DDL Query made to DB:
create table `tabNursing Checklist Template Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`activity` varchar(140),
`mandatory` int(1) not null default 0,
`type` varchar(140),
`time_offset` decimal(21,9),
`description` text,
`task_duration` decimal(21,9),
`task_doctype` varchar(140),
`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:47,466 WARNING database DDL Query made to DB:
create table `tabObservation Reference Range` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`permitted_data_type` varchar(140),
`reference_type` varchar(140),
`applies_to` varchar(140),
`age` varchar(140),
`gestational_age` decimal(21,9) not null default 0,
`from_age_type` varchar(140) default 'Years',
`age_from` varchar(140),
`to_age_type` varchar(140) default 'Years',
`age_to` varchar(140),
`reference_from` varchar(140),
`datetime` datetime(6),
`from_duration` decimal(21,9),
`boolean_value` varchar(140),
`ratio` varchar(140),
`options` varchar(140),
`from_datetime` datetime(6),
`conditions` longtext,
`reference_to` varchar(140),
`to_datetime` datetime(6),
`to_duration` decimal(21,9),
`short_interpretation` varchar(140),
`long_interpretation` text,
`normal_boolean_value` varchar(140),
`normal_ratio` varchar(140),
`normal_from` decimal(21,9) not null default 0,
`normal_select` varchar(140),
`normal_condition` longtext,
`normal_interpretation` varchar(140),
`normal_to` decimal(21,9) not null default 0,
`normal_long_interpretation` text,
`abnormal_boolean_value` varchar(140),
`abnormal_from` decimal(21,9) not null default 0,
`abnormal_ratio` varchar(140),
`abnormal_select` varchar(140),
`abnormal_condition` longtext,
`abnormal_interpretation` varchar(140),
`abnormal_to` decimal(21,9) not null default 0,
`abnormal_long_interpretation` text,
`critical_boolean_value` varchar(140),
`critical_from` decimal(21,9) not null default 0,
`critical_ratio` varchar(140),
`critical_select` varchar(140),
`critical_condition` longtext,
`critical_interpretation` varchar(140),
`critical_to` decimal(21,9) not null default 0,
`critical_long_interpretation` text,
`reference_text` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:52:47,610 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Medication Entry Detail` MODIFY `available_qty` decimal(21,9) not null default 0, MODIFY `dosage` decimal(21,9) not null default 0
2025-05-21 11:52:47,733 WARNING database DDL Query made to DB:
ALTER TABLE `tabNormal Test Result` MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-05-21 11:52:47,993 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` ADD COLUMN `observation_template` varchar(140), ADD COLUMN `service_request` varchar(140), ADD COLUMN `patient_care_type` varchar(140)
2025-05-21 11:52:48,169 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Occupancy` ADD COLUMN `transferred_for_procedure` int(1) not null default 0, ADD COLUMN `scheduled_billing_time` datetime(6)
2025-05-21 12:09:59,459 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-21 12:10:00,376 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-21 12:10:01,570 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-21 12:10:02,743 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-05-21 12:10:03,064 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-05-21 12:10:03,562 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-21 12:10:03,977 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-21 12:10:04,575 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-05-21 12:10:04,607 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `container_no_index`, DROP INDEX `gross_weight_index`, DROP INDEX `net_weight_index`
2025-05-21 12:10:04,951 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-05-21 12:10:05,212 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-05-21 12:10:05,728 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-21 12:10:05,881 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-21 12:10:06,167 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_volume` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-05-21 12:10:06,260 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-05-21 12:10:17,010 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `auto_repeat_end_date` date, MODIFY `hourly_rate` decimal(21,9) not null default 0
2025-05-21 12:10:20,975 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-05-21 12:24:06,599 WARNING database DDL Query made to DB:
ALTER TABLE `tabLimit Change Request` MODIFY `daily_limit` decimal(21,9) not null default 0, MODIFY `previous_cash_limit` decimal(21,9) not null default 0, MODIFY `current_total_deposit` decimal(21,9) not null default 0, MODIFY `previous_daily_limit` decimal(21,9) not null default 0, MODIFY `cash_limit` decimal(21,9) not null default 0, MODIFY `current_total_cost` decimal(21,9) not null default 0
2025-05-23 09:31:12,007 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-23 09:31:14,921 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
