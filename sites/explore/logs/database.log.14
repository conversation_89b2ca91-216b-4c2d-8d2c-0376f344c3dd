2025-02-18 16:17:52,720 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0
2025-02-18 16:17:53,457 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequest for Quotation Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0
2025-02-18 16:17:54,114 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation Item` MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0
2025-02-18 16:17:55,045 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0
2025-02-18 16:17:55,592 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask` MODIFY `actual_time` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `task_weight` decimal(21,9) not null default 0, MODIFY `progress` decimal(21,9) not null default 0, MODIFY `total_expense_claim` decimal(21,9) not null default 0, MODIFY `total_billing_amount` decimal(21,9) not null default 0
2025-02-18 16:17:55,895 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject Update` ADD INDEX `date_index`(`date`)
2025-02-18 16:17:56,256 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` MODIFY `total_purchase_cost` decimal(21,9) not null default 0, MODIFY `estimated_costing` decimal(21,9) not null default 0, MODIFY `total_billable_amount` decimal(21,9) not null default 0, MODIFY `total_sales_amount` decimal(21,9) not null default 0, MODIFY `per_gross_margin` decimal(21,9) not null default 0, MODIFY `gross_margin` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `total_expense_claim` decimal(21,9) not null default 0, MODIFY `total_billed_amount` decimal(21,9) not null default 0, MODIFY `total_consumed_material_cost` decimal(21,9) not null default 0, MODIFY `actual_time` decimal(21,9) not null default 0, MODIFY `percent_complete` decimal(21,9) not null default 0
2025-02-18 16:17:56,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` ADD INDEX `collect_progress_index`(`collect_progress`)
2025-02-18 16:17:57,164 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `reserve_stock` int(1) not null default 0
2025-02-18 16:17:57,189 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0
2025-02-18 16:17:58,382 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` ADD COLUMN `prospect_name` varchar(140)
2025-02-18 16:17:58,405 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-02-18 16:17:58,855 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation Item` ADD COLUMN `company_total_stock` decimal(21,9) not null default 0
2025-02-18 16:17:58,879 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation Item` MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `gross_profit` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `blanket_order_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-02-18 16:17:59,395 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` ADD COLUMN `disable_rounded_total` int(1) not null default 0
2025-02-18 16:17:59,419 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-02-18 16:17:59,881 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` ADD COLUMN `is_stock_item` int(1) not null default 0, ADD COLUMN `reserve_stock` int(1) not null default 1, ADD COLUMN `stock_reserved_qty` decimal(21,9) not null default 0, ADD COLUMN `company_total_stock` decimal(21,9) not null default 0
2025-02-18 16:17:59,908 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `blanket_order_rate` decimal(21,9) not null default 0, MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `produced_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `production_plan_qty` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `gross_profit` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `work_order_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `planned_qty` decimal(21,9) not null default 0
2025-02-18 16:18:00,176 WARNING database DDL Query made to DB:
ALTER TABLE `tabInstallation Note Item` ADD COLUMN `serial_and_batch_bundle` varchar(140)
2025-02-18 16:18:00,198 WARNING database DDL Query made to DB:
ALTER TABLE `tabInstallation Note Item` MODIFY `qty` decimal(21,9) not null default 0
2025-02-18 16:18:00,788 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-02-18 16:18:01,729 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle` MODIFY `uom` varchar(140), MODIFY `vehicle_value` decimal(21,9) not null default 0
2025-02-18 16:18:03,389 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `round_off_for_opening` varchar(140), ADD COLUMN `book_advance_payments_in_separate_party_account` int(1) not null default 0, ADD COLUMN `reconcile_on_advance_payment_date` int(1) not null default 0, ADD COLUMN `reconciliation_takes_effect_on` varchar(140) default 'Oldest Of Invoice Or Advance', ADD COLUMN `default_advance_received_account` varchar(140), ADD COLUMN `default_advance_paid_account` varchar(140), ADD COLUMN `expenses_included_in_asset_valuation` varchar(140), ADD COLUMN `expenses_included_in_valuation` varchar(140), ADD COLUMN `default_operating_cost_account` varchar(140)
2025-02-18 16:18:03,415 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-02-18 16:18:03,779 WARNING database DDL Query made to DB:
ALTER TABLE `tabAuthorization Rule` MODIFY `value` decimal(21,9) not null default 0
2025-02-18 16:18:04,446 WARNING database DDL Query made to DB:
create table `tabPlant Floor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`floor_name` varchar(140) unique,
`company` varchar(140),
`warehouse` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 16:18:04,762 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkstation` ADD COLUMN `plant_floor` varchar(140), ADD COLUMN `warehouse` varchar(140), ADD COLUMN `status` varchar(140), ADD COLUMN `on_status_image` text, ADD COLUMN `off_status_image` text, ADD COLUMN `total_working_hours` decimal(21,9) not null default 0
2025-02-18 16:18:04,783 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkstation` MODIFY `hour_rate_rent` decimal(21,9) not null default 0, MODIFY `hour_rate_electricity` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `hour_rate_labour` decimal(21,9) not null default 0, MODIFY `hour_rate_consumable` decimal(21,9) not null default 0
2025-02-18 16:18:05,085 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Item` MODIFY `planned_qty` decimal(21,9) not null default 0
2025-02-18 16:18:05,334 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Operation` MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `cost_per_unit` decimal(21,9) not null default 0, MODIFY `base_cost_per_unit` decimal(21,9) not null default 0, MODIFY `time_in_mins` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0
2025-02-18 16:18:05,590 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkstation Working Hour` ADD COLUMN `hours` decimal(21,9) not null default 0
2025-02-18 16:18:06,075 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` ADD COLUMN `bom_creator` varchar(140), ADD COLUMN `bom_creator_item` varchar(140)
2025-02-18 16:18:06,095 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0
2025-02-18 16:18:06,523 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card` ADD COLUMN `time_required` decimal(21,9) not null default 0, ADD COLUMN `actual_start_date` datetime(6), ADD COLUMN `actual_end_date` datetime(6), ADD COLUMN `serial_and_batch_bundle` varchar(140)
2025-02-18 16:18:06,544 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card` MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `for_quantity` decimal(21,9) not null default 0, MODIFY `total_time_in_mins` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0
2025-02-18 16:18:06,899 WARNING database DDL Query made to DB:
ALTER TABLE `tabBlanket Order` ADD COLUMN `order_no` varchar(140), ADD COLUMN `order_date` date
2025-02-18 16:18:07,140 WARNING database DDL Query made to DB:
create table `tabJob Card Scheduled Time` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_time` datetime(6),
`to_time` datetime(6),
`time_in_mins` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 16:18:07,911 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `additional_operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `total_operating_cost` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9) not null default 0, MODIFY `corrective_operation_cost` decimal(21,9) not null default 0
2025-02-18 16:18:08,176 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card Time Log` MODIFY `time_in_mins` decimal(21,9) not null default 0
2025-02-18 16:18:08,494 WARNING database DDL Query made to DB:
create table `tabBOM Creator` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`item_group` varchar(140),
`qty` decimal(21,9) not null default 0,
`project` varchar(140),
`uom` varchar(140),
`rm_cost_as_per` varchar(140) default 'Valuation Rate',
`set_rate_based_on_warehouse` int(1) not null default 0,
`buying_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) not null default 0,
`currency` varchar(140),
`conversion_rate` decimal(21,9) not null default 1.0,
`default_warehouse` varchar(140),
`company` varchar(140),
`raw_material_cost` decimal(21,9) not null default 0,
`remarks` longtext,
`status` varchar(140) default 'Draft',
`error_log` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 16:18:08,761 WARNING database DDL Query made to DB:
create table `tabBOM Creator Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`item_group` varchar(140),
`fg_item` varchar(140),
`source_warehouse` varchar(140),
`is_expandable` int(1) not null default 0,
`sourced_by_supplier` int(1) not null default 0,
`bom_created` int(1) not null default 0,
`description` text,
`qty` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`uom` varchar(140),
`stock_qty` decimal(21,9) not null default 0,
`conversion_factor` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`amount` decimal(21,9) not null default 0,
`base_rate` decimal(21,9) not null default 0,
`base_amount` decimal(21,9) not null default 0,
`do_not_explode` int(1) not null default 1,
`parent_row_no` varchar(140),
`fg_reference_id` varchar(140),
`instruction` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 16:18:09,575 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Price` MODIFY `price_list_rate` decimal(21,9) not null default 0
2025-02-18 16:18:10,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` ADD COLUMN `use_serial_batch_fields` int(1) not null default 0, ADD COLUMN `serial_and_batch_bundle` varchar(140)
2025-02-18 16:18:10,022 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `additional_cost` decimal(21,9) not null default 0, MODIFY `basic_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `transferred_qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `basic_amount` decimal(21,9) not null default 0
2025-02-18 16:18:10,331 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` ADD COLUMN `ignore_pricing_rule` int(1) not null default 0
2025-02-18 16:18:10,353 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` MODIFY `for_qty` decimal(21,9) not null default 0
2025-02-18 16:18:10,842 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `use_serial_batch_fields` int(1) not null default 0, ADD COLUMN `company_total_stock` decimal(21,9) not null default 0
2025-02-18 16:18:10,864 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `installed_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0
2025-02-18 16:18:10,896 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD INDEX `serial_and_batch_bundle_index`(`serial_and_batch_bundle`)
2025-02-18 16:18:11,494 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` ADD COLUMN `use_serial_batch_fields` int(1) not null default 0, ADD COLUMN `reconcile_all_serial_batch` int(1) not null default 0, ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `current_serial_and_batch_bundle` varchar(140)
2025-02-18 16:18:11,518 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount_difference` decimal(21,9) not null default 0, MODIFY `current_amount` decimal(21,9) not null default 0, MODIFY `current_valuation_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0
2025-02-18 16:18:11,549 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` ADD INDEX `serial_and_batch_bundle_index`(`serial_and_batch_bundle`)
2025-02-18 16:18:12,050 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` ADD COLUMN `asset_repair` varchar(140)
2025-02-18 16:18:12,071 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `repack_qty` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0
2025-02-18 16:18:12,402 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Attribute` ADD COLUMN `disabled` int(1) not null default 0
2025-02-18 16:18:12,689 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Item Valuation` ADD COLUMN `reposting_data_file` text, ADD COLUMN `total_reposting_count` int(11) not null default 0
2025-02-18 16:18:13,272 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `total_projected_qty` decimal(21,9) not null default 0, MODIFY `over_delivery_receipt_allowance` decimal(21,9) not null default 0, MODIFY `standard_rate` decimal(21,9) not null default 0, MODIFY `witholding_tax_rate_on_purchase` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `opening_stock` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate_on_sales` decimal(21,9) not null default 0, MODIFY `safety_stock` decimal(21,9) not null default 0, MODIFY `over_billing_allowance` decimal(21,9) not null default 0, MODIFY `max_discount` decimal(21,9) not null default 0
2025-02-18 16:18:13,614 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Variant Attribute` ADD COLUMN `disabled` int(1) not null default 0
2025-02-18 16:18:13,636 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Variant Attribute` MODIFY `from_range` decimal(21,9) not null default 0, MODIFY `increment` decimal(21,9) not null default 0, MODIFY `to_range` decimal(21,9) not null default 0
2025-02-18 16:18:13,934 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` ADD COLUMN `auto_created_serial_and_batch_bundle` int(1) not null default 0, ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `has_batch_no` int(1) not null default 0, ADD COLUMN `has_serial_no` int(1) not null default 0
2025-02-18 16:18:13,960 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` MODIFY `qty_after_transaction` decimal(21,9) not null default 0, MODIFY `outgoing_rate` decimal(21,9) not null default 0, MODIFY `stock_value_difference` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_value` decimal(21,9) not null default 0
2025-02-18 16:18:14,017 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` ADD INDEX `posting_date_index`(`posting_date`), ADD INDEX `voucher_type_index`(`voucher_type`), ADD INDEX `serial_and_batch_bundle_index`(`serial_and_batch_bundle`)
2025-02-18 16:18:15,387 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` ADD COLUMN `use_serial_batch_fields` int(1) not null default 0, ADD COLUMN `serial_and_batch_bundle` varchar(140)
2025-02-18 16:18:16,140 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-02-18 16:18:16,769 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Inspection` ADD COLUMN `company` varchar(140), ADD COLUMN `child_row_reference` varchar(140)
2025-02-18 16:18:16,793 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Inspection` MODIFY `sample_size` decimal(21,9) not null default 0
2025-02-18 16:18:17,375 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` ADD COLUMN `apply_tds` int(1) not null default 1, ADD COLUMN `sales_incoming_rate` decimal(21,9) not null default 0, ADD COLUMN `return_qty_from_rejected_warehouse` int(1) not null default 0, ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `use_serial_batch_fields` int(1) not null default 0, ADD COLUMN `rejected_serial_and_batch_bundle` varchar(140), ADD COLUMN `subcontracting_receipt_item` varchar(140)
2025-02-18 16:18:17,397 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `rate_difference_with_purchase_invoice` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `received_stock_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-02-18 16:18:17,444 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` ADD INDEX `serial_and_batch_bundle_index`(`serial_and_batch_bundle`), ADD INDEX `batch_no_index`(`batch_no`), ADD INDEX `subcontracting_receipt_item_index`(`subcontracting_receipt_item`)
2025-02-18 16:18:18,187 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `per_installed` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-02-18 16:18:18,541 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Type` ADD COLUMN `is_standard` int(1) not null default 0
2025-02-18 16:18:18,804 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Reorder` MODIFY `warehouse_reorder_qty` decimal(21,9) not null default 0, MODIFY `warehouse_reorder_level` decimal(21,9) not null default 0
2025-02-18 16:18:18,995 WARNING database DDL Query made to DB:
create table `tabSerial and Batch Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`serial_no` varchar(140),
`batch_no` varchar(140),
`qty` decimal(21,9) not null default 1.0,
`warehouse` varchar(140),
`delivered_qty` decimal(21,9) not null default 0,
`incoming_rate` decimal(21,9) not null default 0,
`outgoing_rate` decimal(21,9) not null default 0,
`stock_value_difference` decimal(21,9) not null default 0,
`is_outward` int(1) not null default 0,
`stock_queue` text,
index `serial_no`(`serial_no`),
index `batch_no`(`batch_no`),
index `warehouse`(`warehouse`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 16:18:19,637 WARNING database DDL Query made to DB:
ALTER TABLE `tabPutaway Rule` MODIFY `stock_capacity` decimal(21,9) not null default 0
2025-02-18 16:18:19,919 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` MODIFY `grand_total` decimal(21,9) not null default 0
2025-02-18 16:18:20,207 WARNING database DDL Query made to DB:
ALTER TABLE `tabSerial No` MODIFY `purchase_rate` decimal(21,9) not null default 0
2025-02-18 16:18:20,232 WARNING database DDL Query made to DB:
ALTER TABLE `tabSerial No` DROP INDEX `warehouse`
2025-02-18 16:18:20,891 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt` ADD COLUMN `subcontracting_receipt` varchar(140), ADD COLUMN `tax_withholding_net_total` decimal(21,9) not null default 0, ADD COLUMN `base_tax_withholding_net_total` decimal(21,9) not null default 0
2025-02-18 16:18:20,914 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt` MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0
2025-02-18 16:18:20,948 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt` ADD INDEX `subcontracting_receipt_index`(`subcontracting_receipt`)
2025-02-18 16:18:21,279 WARNING database DDL Query made to DB:
create table `tabSerial and Batch Bundle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`company` varchar(140),
`item_name` varchar(140),
`has_serial_no` int(1) not null default 0,
`has_batch_no` int(1) not null default 0,
`item_code` varchar(140),
`warehouse` varchar(140),
`type_of_transaction` varchar(140),
`total_qty` decimal(21,9) not null default 0,
`item_group` varchar(140),
`avg_rate` decimal(21,9) not null default 0,
`total_amount` decimal(21,9) not null default 0,
`voucher_type` varchar(140),
`voucher_no` varchar(140),
`voucher_detail_no` varchar(140),
`posting_date` date,
`posting_time` time(6),
`returned_against` varchar(140),
`is_cancelled` int(1) not null default 0,
`is_rejected` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `item_code`(`item_code`),
index `warehouse`(`warehouse`),
index `type_of_transaction`(`type_of_transaction`),
index `voucher_no`(`voucher_no`),
index `voucher_detail_no`(`voucher_detail_no`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 16:18:21,652 WARNING database DDL Query made to DB:
ALTER TABLE `tabBin` ADD COLUMN `reserved_stock` decimal(21,9) not null default 0
2025-02-18 16:18:21,673 WARNING database DDL Query made to DB:
ALTER TABLE `tabBin` MODIFY `reserved_qty_for_sub_contract` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `stock_value` decimal(21,9) not null default 0, MODIFY `reserved_qty_for_production` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `planned_qty` decimal(21,9) not null default 0, MODIFY `reserved_qty_for_production_plan` decimal(21,9) not null default 0
2025-02-18 16:18:22,039 WARNING database DDL Query made to DB:
create table `tabStock Reservation Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`warehouse` varchar(140),
`has_serial_no` int(1) not null default 0,
`has_batch_no` int(1) not null default 0,
`voucher_type` varchar(140),
`voucher_no` varchar(140),
`voucher_detail_no` varchar(140),
`from_voucher_type` varchar(140),
`from_voucher_no` varchar(140),
`from_voucher_detail_no` varchar(140),
`stock_uom` varchar(140),
`available_qty` decimal(21,9) not null default 0,
`voucher_qty` decimal(21,9) not null default 0,
`reserved_qty` decimal(21,9) not null default 0,
`delivered_qty` decimal(21,9) not null default 0,
`reservation_based_on` varchar(140) default 'Qty',
`company` varchar(140),
`project` varchar(140),
`status` varchar(140) default 'Draft',
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `item_code`(`item_code`),
index `warehouse`(`warehouse`),
index `voucher_no`(`voucher_no`),
index `voucher_detail_no`(`voucher_detail_no`),
index `from_voucher_no`(`from_voucher_no`),
index `company`(`company`),
index `project`(`project`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 16:18:22,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `min_order_qty` decimal(21,9) not null default 0
2025-02-18 16:18:23,351 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` ADD COLUMN `stock_reserved_qty` decimal(21,9) not null default 0, ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `use_serial_batch_fields` int(1) not null default 0
2025-02-18 16:18:23,372 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0
2025-02-18 16:18:23,404 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` ADD INDEX `serial_and_batch_bundle_index`(`serial_and_batch_bundle`)
2025-02-18 16:18:23,429 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` DROP INDEX `sales_order_item`
2025-02-18 16:18:23,779 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Trip` MODIFY `total_distance` decimal(21,9) not null default 0
2025-02-18 16:18:25,473 WARNING database DDL Query made to DB:
create table `tabPortal User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
index `user`(`user`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 16:18:25,785 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Shift Allocation` ADD INDEX `amended_from_index`(`amended_from`)
2025-02-18 16:18:26,233 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair` MODIFY `total_repair_cost` decimal(21,9) not null default 0
2025-02-18 16:18:26,645 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization` MODIFY `stock_items_total` decimal(21,9) not null default 0, MODIFY `asset_items_total` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `target_incoming_rate` decimal(21,9) not null default 0, MODIFY `service_items_total` decimal(21,9) not null default 0
2025-02-18 16:18:26,952 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Stock Item` ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `use_serial_batch_fields` int(1) not null default 0
2025-02-18 16:18:26,973 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Stock Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0
2025-02-18 16:18:27,429 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` ADD COLUMN `purchase_receipt_item` varchar(140), ADD COLUMN `purchase_invoice_item` varchar(140), ADD COLUMN `opening_number_of_booked_depreciations` int(11) not null default 0, ADD COLUMN `purchase_amount` decimal(21,9) not null default 0
2025-02-18 16:18:27,451 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` MODIFY `total_asset_cost` decimal(21,9) not null default 0, MODIFY `opening_accumulated_depreciation` decimal(21,9) not null default 0, MODIFY `additional_asset_cost` decimal(21,9) not null default 0, MODIFY `value_after_depreciation` decimal(21,9) not null default 0, MODIFY `gross_purchase_amount` decimal(21,9) not null default 0
2025-02-18 16:18:27,701 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair Consumed Item` ADD COLUMN `warehouse` varchar(140), ADD COLUMN `serial_and_batch_bundle` varchar(140)
2025-02-18 16:18:27,723 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair Consumed Item` MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0
2025-02-18 16:18:27,924 WARNING database DDL Query made to DB:
create table `tabAsset Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset` varchar(140),
`date` datetime(6),
`user` varchar(140),
`subject` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 16:18:28,234 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Finance Book` ADD COLUMN `total_number_of_booked_depreciations` int(11) not null default 0
2025-02-18 16:18:28,254 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Finance Book` MODIFY `salvage_value_percentage` decimal(21,9) not null default 0, MODIFY `rate_of_depreciation` decimal(21,9) not null default 0, MODIFY `value_after_depreciation` decimal(21,9) not null default 0
2025-02-18 16:18:28,518 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepreciation Schedule` MODIFY `accumulated_depreciation_amount` decimal(21,9) not null default 0, MODIFY `depreciation_amount` decimal(21,9) not null default 0
2025-02-18 16:18:28,833 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Maintenance Log` ADD COLUMN `task_assignee_email` varchar(140)
2025-02-18 16:18:29,077 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Service Item` MODIFY `rate` decimal(21,9) not null default 0
2025-02-18 16:18:29,302 WARNING database DDL Query made to DB:
create table `tabAsset Depreciation Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset` varchar(140),
`naming_series` varchar(140),
`company` varchar(140),
`gross_purchase_amount` decimal(21,9) not null default 0,
`opening_accumulated_depreciation` decimal(21,9) not null default 0,
`opening_number_of_booked_depreciations` int(11) not null default 0,
`finance_book` varchar(140),
`finance_book_id` int(11) not null default 0,
`depreciation_method` varchar(140),
`total_number_of_depreciations` int(11) not null default 0,
`rate_of_depreciation` decimal(21,9) not null default 0,
`daily_prorata_based` int(1) not null default 0,
`shift_based` int(1) not null default 0,
`frequency_of_depreciation` int(11) not null default 0,
`expected_value_after_useful_life` decimal(21,9) not null default 0,
`notes` text,
`status` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 16:18:29,576 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Shift Factor` MODIFY `shift_factor` decimal(21,9) not null default 0
2025-02-18 16:18:30,230 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaintenance Schedule Item` ADD COLUMN `serial_and_batch_bundle` varchar(140)
2025-02-18 16:18:32,930 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Item` ADD COLUMN `purchase_order_item` varchar(140), ADD COLUMN `sc_conversion_factor` decimal(21,9) not null default 0
2025-02-18 16:18:32,955 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Item` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `rm_cost_per_qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `service_cost_per_qty` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0
2025-02-18 16:18:32,986 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Item` ADD INDEX `purchase_order_item_index`(`purchase_order_item`)
2025-02-18 16:18:33,249 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Service Item` ADD COLUMN `purchase_order_item` varchar(140)
2025-02-18 16:18:33,270 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Service Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-02-18 16:18:33,300 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Service Item` ADD INDEX `purchase_order_item_index`(`purchase_order_item`)
2025-02-18 16:18:33,623 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `use_serial_batch_fields` int(1) not null default 0
2025-02-18 16:18:33,644 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `required_qty` decimal(21,9) not null default 0, MODIFY `consumed_qty` decimal(21,9) not null default 0, MODIFY `current_stock` decimal(21,9) not null default 0
2025-02-18 16:18:34,020 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Item` ADD COLUMN `is_scrap_item` int(1) not null default 0, ADD COLUMN `scrap_cost_per_qty` decimal(21,9) not null default 0, ADD COLUMN `reference_name` varchar(140), ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `use_serial_batch_fields` int(1) not null default 0, ADD COLUMN `rejected_serial_and_batch_bundle` varchar(140), ADD COLUMN `purchase_order` varchar(140), ADD COLUMN `purchase_order_item` varchar(140)
2025-02-18 16:18:34,042 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-02-18 16:18:34,080 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Item` ADD INDEX `purchase_order_index`(`purchase_order`), ADD INDEX `purchase_order_item_index`(`purchase_order_item`)
2025-02-18 16:18:34,541 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt` MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-02-18 16:18:34,843 WARNING database DDL Query made to DB:
create table `tabSubcontracting BOM` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_active` int(1) not null default 1,
`finished_good` varchar(140),
`finished_good_qty` decimal(21,9) not null default 1.0,
`finished_good_uom` varchar(140),
`finished_good_bom` varchar(140),
`service_item` varchar(140),
`service_item_qty` decimal(21,9) not null default 1.0,
`service_item_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `finished_good`(`finished_good`),
index `finished_good_bom`(`finished_good_bom`),
index `service_item`(`service_item`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 16:18:35,356 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order` MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0
2025-02-18 16:18:35,736 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Requisition` MODIFY `expected_compensation` decimal(21,9) not null default 0, MODIFY `time_to_fill` decimal(21,9)
2025-02-18 16:18:35,770 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Requisition` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:36,030 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Boarding Activity` MODIFY `task_weight` decimal(21,9) not null default 0
2025-02-18 16:18:36,064 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Boarding Activity` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:36,404 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer Term` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:36,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Cycle` ADD COLUMN `calculate_final_score_based_on_formula` int(1) not null default 0, ADD COLUMN `final_score_formula` longtext
2025-02-18 16:18:36,749 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Cycle` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:37,075 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:37,320 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpected Skill Set` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:37,548 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter content` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:37,821 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Date` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:38,063 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer Term Template` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:38,335 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:38,597 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Separation Template` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:38,907 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Request` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:39,147 WARNING database DDL Query made to DB:
create sequence if not exists pwa_notification_id_seq nocache nocycle
2025-02-18 16:18:39,169 WARNING database DDL Query made to DB:
create table `tabPWA Notification` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`to_user` varchar(140),
`from_user` varchar(140),
`message` longtext,
`read` int(1) not null default 0,
`reference_document_type` varchar(140),
`reference_document_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `to_user`(`to_user`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 16:18:39,450 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Feedback` MODIFY `average_rating` decimal(3,2)
2025-02-18 16:18:39,486 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Feedback` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:39,751 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisee` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:40,123 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Checkin` ADD COLUMN `latitude` decimal(21,9) not null default 0, ADD COLUMN `longitude` decimal(21,9) not null default 0, ADD COLUMN `geolocation` longtext, ADD COLUMN `offshift` int(1) not null default 0
2025-02-18 16:18:40,165 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Checkin` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:40,674 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-02-18 16:18:40,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:41,027 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD COLUMN `actual_encashable_days` decimal(21,9) not null default 0, ADD COLUMN `encashment_days` decimal(21,9) not null default 0
2025-02-18 16:18:41,049 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` MODIFY `leave_balance` decimal(21,9) not null default 0, MODIFY `encashment_amount` decimal(21,9) not null default 0
2025-02-18 16:18:41,094 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:41,394 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter Template` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:41,810 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `pending_amount` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0
2025-02-18 16:18:41,842 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:42,132 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Assignment` ADD COLUMN `shift_location` varchar(140), ADD COLUMN `shift_schedule_assignment` varchar(140)
2025-02-18 16:18:42,164 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Assignment` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:42,412 WARNING database DDL Query made to DB:
create table `tabShift Schedule Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`shift_schedule` varchar(140),
`shift_location` varchar(140),
`shift_status` varchar(140) default 'Active',
`enabled` int(1) not null default 1,
`create_shifts_after` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 16:18:42,722 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Allow` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:42,973 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Asset` ADD COLUMN `actual_cost` decimal(21,9) not null default 0, ADD COLUMN `cost` decimal(21,9) not null default 0, ADD COLUMN `account` varchar(140), ADD COLUMN `action` varchar(140) default 'Return'
2025-02-18 16:18:43,011 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Asset` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:43,496 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Onboarding Template` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:43,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Referral` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:44,151 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Period` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:44,519 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service Item` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:44,794 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Detail` MODIFY `sanctioned_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-02-18 16:18:44,824 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Detail` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:45,114 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Itinerary` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:45,433 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Feedback` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:45,686 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Training` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:45,900 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal KRA` MODIFY `goal_score` decimal(21,9) not null default 0, MODIFY `goal_completion` decimal(21,9) not null default 0, MODIFY `per_weightage` decimal(21,9) not null default 0
2025-02-18 16:18:45,931 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal KRA` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:46,154 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Outstanding Statement` MODIFY `amount` decimal(21,9) not null default 0
2025-02-18 16:18:46,185 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Outstanding Statement` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:46,458 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoal` MODIFY `progress` decimal(21,9) not null default 0
2025-02-18 16:18:46,492 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoal` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:46,886 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview` MODIFY `expected_average_rating` decimal(3,2), MODIFY `average_rating` decimal(3,2)
2025-02-18 16:18:46,919 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:47,215 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:47,465 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Detail` MODIFY `annual_allocation` decimal(21,9) not null default 0
2025-02-18 16:18:47,498 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Detail` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:47,690 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill Assessment` MODIFY `rating` decimal(3,2)
2025-02-18 16:18:47,720 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill Assessment` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:47,996 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant Source` ADD UNIQUE INDEX IF NOT EXISTS source_name (`source_name`)
2025-02-18 16:18:48,029 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant Source` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:48,285 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterest` ADD UNIQUE INDEX IF NOT EXISTS interest (`interest`)
2025-02-18 16:18:48,316 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterest` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:48,605 WARNING database DDL Query made to DB:
ALTER TABLE `tabKRA` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:48,850 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment Approver` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:49,082 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Criteria` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:49,455 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:49,767 WARNING database DDL Query made to DB:
ALTER TABLE `tabExit Interview` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:50,063 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:50,416 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Opening` ADD COLUMN `posted_on` datetime(6), ADD COLUMN `closes_on` date, ADD COLUMN `closed_on` date, ADD COLUMN `employment_type` varchar(140), ADD COLUMN `location` varchar(140), ADD COLUMN `publish_applications_received` int(1) not null default 1, ADD COLUMN `salary_per` varchar(140) default 'Month'
2025-02-18 16:18:50,438 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Opening` MODIFY `lower_range` decimal(21,9) not null default 0, MODIFY `upper_range` decimal(21,9) not null default 0
2025-02-18 16:18:50,468 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Opening` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:50,779 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application` MODIFY `leave_balance` decimal(21,9) not null default 0, MODIFY `total_leave_days` decimal(21,9) not null default 0
2025-02-18 16:18:50,822 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:50,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application`
				ADD INDEX `employee_from_date_to_date_index`(employee, from_date, to_date)
2025-02-18 16:18:51,163 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Round` MODIFY `expected_average_rating` decimal(3,2)
2025-02-18 16:18:51,202 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Round` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:51,478 WARNING database DDL Query made to DB:
ALTER TABLE `tabOffer Term` ADD UNIQUE INDEX IF NOT EXISTS offer_term (`offer_term`)
2025-02-18 16:18:51,512 WARNING database DDL Query made to DB:
ALTER TABLE `tabOffer Term` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:51,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim` MODIFY `total_claimed_amount` decimal(21,9) not null default 0, MODIFY `total_advance_amount` decimal(21,9) not null default 0, MODIFY `total_sanctioned_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_amount_reimbursed` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0
2025-02-18 16:18:52,016 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:52,372 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Allocation` MODIFY `new_leaves_allocated` decimal(21,9) not null default 0, MODIFY `total_leaves_allocated` decimal(21,9) not null default 0, MODIFY `carry_forwarded_leaves_count` decimal(21,9) not null default 0, MODIFY `unused_leaves` decimal(21,9) not null default 0, MODIFY `total_leaves_encashed` decimal(21,9) not null default 0
2025-02-18 16:18:52,405 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Allocation` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:52,691 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Onboarding` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:52,958 WARNING database DDL Query made to DB:
ALTER TABLE `tabIdentification Document Type` ADD UNIQUE INDEX IF NOT EXISTS identification_document_type (`identification_document_type`)
2025-02-18 16:18:52,997 WARNING database DDL Query made to DB:
ALTER TABLE `tabIdentification Document Type` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:53,300 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance Request` ADD COLUMN `shift` varchar(140)
2025-02-18 16:18:53,331 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance Request` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:53,630 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Goal` MODIFY `per_weightage` decimal(21,9) not null default 0, MODIFY `score_earned` decimal(21,9) not null default 0, MODIFY `score` decimal(21,9) not null default 0
2025-02-18 16:18:53,662 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Goal` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:53,865 WARNING database DDL Query made to DB:
create table `tabShift Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_type` varchar(140),
`frequency` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 16:18:54,621 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grievance` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:54,894 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Type` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:55,183 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Taxes and Charges` MODIFY `tax_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-02-18 16:18:55,215 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Taxes and Charges` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:55,433 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill` MODIFY `proficiency` decimal(3,2)
2025-02-18 16:18:55,464 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:55,730 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployment Type` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:56,113 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:56,377 WARNING database DDL Query made to DB:
ALTER TABLE `tabGrievance Type` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:56,604 WARNING database DDL Query made to DB:
create table `tabShift Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location_name` varchar(140) unique,
`checkin_radius` int(11) not null default 0,
`latitude` decimal(21,9) not null default 0,
`longitude` decimal(21,9) not null default 0,
`geolocation` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 16:18:56,920 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant` MODIFY `upper_range` decimal(21,9) not null default 0, MODIFY `lower_range` decimal(21,9) not null default 0, MODIFY `applicant_rating` decimal(3,2)
2025-02-18 16:18:56,956 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant` ADD INDEX `job_title_index`(`job_title`), ADD INDEX `creation`(`creation`)
2025-02-18 16:18:57,173 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:57,443 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0
2025-02-18 16:18:57,484 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `attendance_date_index`(`attendance_date`), ADD INDEX `creation`(`creation`)
2025-02-18 16:18:57,770 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Property History` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:58,028 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary Group User` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:58,287 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` ADD COLUMN `color` varchar(140) default 'Blue', ADD COLUMN `enable_late_entry_marking` int(1) not null default 0, ADD COLUMN `enable_early_exit_marking` int(1) not null default 0
2025-02-18 16:18:58,308 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` MODIFY `working_hours_threshold_for_half_day` decimal(21,9) not null default 0, MODIFY `working_hours_threshold_for_absent` decimal(21,9) not null default 0
2025-02-18 16:18:58,345 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:58,663 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event Employee` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:58,879 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Ledger Entry` MODIFY `leaves` decimal(21,9) not null default 0
2025-02-18 16:18:58,916 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Ledger Entry` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:59,199 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:59,468 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill Map` ADD INDEX `creation`(`creation`)
2025-02-18 16:18:59,688 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grade` MODIFY `default_base_pay` decimal(21,9) not null default 0
2025-02-18 16:18:59,738 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grade` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:00,016 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Assignment` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:00,479 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:00,757 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result Employee` MODIFY `hours` decimal(21,9) not null default 0
2025-02-18 16:19:00,790 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result Employee` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:01,076 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterviewer` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:01,356 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompensatory Leave Request` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:01,635 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan Detail` MODIFY `estimated_cost_per_position` decimal(21,9) not null default 0, MODIFY `total_estimated_cost` decimal(21,9) not null default 0
2025-02-18 16:19:01,680 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan Detail` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:01,924 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Type` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:02,182 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurpose of Travel` ADD UNIQUE INDEX IF NOT EXISTS purpose_of_travel (`purpose_of_travel`)
2025-02-18 16:19:02,216 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurpose of Travel` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:02,442 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Account` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:02,693 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Promotion` MODIFY `current_ctc` decimal(21,9) not null default 0, MODIFY `revised_ctc` decimal(21,9) not null default 0
2025-02-18 16:19:02,724 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Promotion` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:03,091 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal` MODIFY `final_score` decimal(21,9) not null default 0, MODIFY `total_score` decimal(21,9) not null default 0, MODIFY `avg_feedback_score` decimal(21,9) not null default 0, MODIFY `self_score` decimal(21,9) not null default 0, MODIFY `goal_score_percentage` decimal(21,9) not null default 0
2025-02-18 16:19:03,122 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:03,511 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Type` ADD COLUMN `max_encashable_leaves` int(11) not null default 0, ADD COLUMN `non_encashable_leaves` int(11) not null default 0
2025-02-18 16:19:03,532 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Type` MODIFY `max_leaves_allowed` decimal(21,9) not null default 0, MODIFY `maximum_carry_forwarded_leaves` decimal(21,9) not null default 0, MODIFY `fraction_of_daily_salary_per_leave` decimal(21,9) not null default 0
2025-02-18 16:19:03,567 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Type` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:03,854 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Advance` ADD COLUMN `return_amount` decimal(21,9) not null default 0
2025-02-18 16:19:03,874 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Advance` MODIFY `unclaimed_amount` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0
2025-02-18 16:19:03,906 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Advance` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:04,404 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Transfer` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:04,721 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Separation` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:04,946 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:05,269 WARNING database DDL Query made to DB:
ALTER TABLE `tabDesignation Skill` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:05,514 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Statement` ADD COLUMN `total_asset_recovery_cost` decimal(21,9) not null default 0
2025-02-18 16:19:05,539 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Statement` MODIFY `total_receivable_amount` decimal(21,9) not null default 0, MODIFY `total_payable_amount` decimal(21,9) not null default 0
2025-02-18 16:19:05,580 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Statement` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:05,842 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-02-18 16:19:05,879 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:06,182 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Performance Feedback` MODIFY `total_score` decimal(21,9) not null default 0
2025-02-18 16:19:06,222 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Performance Feedback` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:06,535 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request` MODIFY `total_travel_cost` decimal(21,9) not null default 0
2025-02-18 16:19:06,578 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:07,037 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary Group` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:07,253 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Health Insurance` ADD UNIQUE INDEX IF NOT EXISTS health_insurance_name (`health_insurance_name`)
2025-02-18 16:19:07,300 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Health Insurance` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:07,564 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Detail` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:07,795 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Rating` MODIFY `rating` decimal(3,2), MODIFY `per_weightage` decimal(21,9) not null default 0
2025-02-18 16:19:07,833 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Rating` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:08,113 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Program` ADD UNIQUE INDEX IF NOT EXISTS training_program (`training_program`)
2025-02-18 16:19:08,145 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Program` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:08,360 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template Goal` MODIFY `per_weightage` decimal(21,9) not null default 0
2025-02-18 16:19:08,395 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template Goal` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:10,391 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration Category` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_amount` decimal(21,9) not null default 0
2025-02-18 16:19:10,426 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration Category` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:10,603 WARNING database DDL Query made to DB:
create table `tabSalary Withholding Cycle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_date` date,
`to_date` date,
`is_salary_released` int(1) not null default 0,
`journal_entry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 16:19:10,997 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Category` MODIFY `max_amount` decimal(21,9) not null default 0
2025-02-18 16:19:11,032 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Category` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:11,332 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:11,573 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Other Income` MODIFY `amount` decimal(21,9) not null default 0
2025-02-18 16:19:11,648 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Other Income` ADD INDEX `employee_index`(`employee`), ADD INDEX `company_index`(`company`), ADD INDEX `payroll_period_index`(`payroll_period`), ADD INDEX `creation`(`creation`)
2025-02-18 16:19:11,962 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission` MODIFY `exemption_amount` decimal(21,9) not null default 0, MODIFY `total_actual_amount` decimal(21,9) not null default 0
2025-02-18 16:19:11,993 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:12,263 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Period Date` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:12,464 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application Detail` MODIFY `max_benefit_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-02-18 16:19:12,511 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application Detail` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:12,745 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Period` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:12,998 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` ADD COLUMN `attach_proof` text
2025-02-18 16:19:13,024 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` MODIFY `max_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-02-18 16:19:13,065 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:13,340 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure Assignment` MODIFY `base` decimal(21,9) not null default 0, MODIFY `taxable_earnings_till_date` decimal(21,9) not null default 0, MODIFY `variable` decimal(21,9) not null default 0, MODIFY `tax_deducted_till_date` decimal(21,9) not null default 0
2025-02-18 16:19:13,378 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure Assignment` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:13,671 WARNING database DDL Query made to DB:
ALTER TABLE `tabRetention Bonus` MODIFY `bonus_amount` decimal(21,9) not null default 0
2025-02-18 16:19:13,718 WARNING database DDL Query made to DB:
ALTER TABLE `tabRetention Bonus` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:14,201 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD COLUMN `salary_withholding` varchar(140), ADD COLUMN `salary_withholding_cycle` varchar(140)
2025-02-18 16:19:14,233 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0
2025-02-18 16:19:14,306 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD INDEX `start_date_index`(`start_date`), ADD INDEX `end_date_index`(`end_date`), ADD INDEX `payroll_entry_index`(`payroll_entry`), ADD INDEX `creation`(`creation`)
2025-02-18 16:19:14,527 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip`
				ADD INDEX `employee_start_date_end_date_index`(employee, start_date, end_date)
2025-02-18 16:19:14,703 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Claim` MODIFY `max_amount_eligible` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0
2025-02-18 16:19:14,750 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Claim` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:15,051 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Incentive` MODIFY `incentive_amount` decimal(21,9) not null default 0
2025-02-18 16:19:15,098 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Incentive` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:15,374 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Cost Center` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:15,869 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component Account` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:16,199 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Employee Detail` ADD COLUMN `is_salary_withheld` int(1) not null default 0
2025-02-18 16:19:16,232 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Employee Detail` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:16,474 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `overwrite_salary_structure_amount` int(1) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-02-18 16:19:16,520 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:16,868 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD COLUMN `grade` varchar(140)
2025-02-18 16:19:16,889 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-02-18 16:19:16,920 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:17,176 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration` MODIFY `total_exemption_amount` decimal(21,9) not null default 0, MODIFY `total_declared_amount` decimal(21,9) not null default 0
2025-02-18 16:19:17,217 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:17,506 WARNING database DDL Query made to DB:
ALTER TABLE `tabTaxable Salary Slab` MODIFY `to_amount` decimal(21,9) not null default 0
2025-02-18 16:19:17,550 WARNING database DDL Query made to DB:
ALTER TABLE `tabTaxable Salary Slab` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:17,796 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Timesheet` MODIFY `working_hours` decimal(21,9) not null default 0
2025-02-18 16:19:17,841 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Timesheet` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:18,078 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Loan` ADD COLUMN `loan_product` varchar(140)
2025-02-18 16:19:18,101 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Loan` MODIFY `principal_amount` decimal(21,9) not null default 0, MODIFY `interest_amount` decimal(21,9) not null default 0, MODIFY `total_payment` decimal(21,9) not null default 0
2025-02-18 16:19:18,310 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab Other Charges` MODIFY `min_taxable_income` decimal(21,9) not null default 0, MODIFY `percent` decimal(21,9) not null default 0, MODIFY `max_taxable_income` decimal(21,9) not null default 0
2025-02-18 16:19:18,347 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab Other Charges` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:18,610 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `tax_on_additional_salary` decimal(21,9) not null default 0, MODIFY `default_amount` decimal(21,9) not null default 0, MODIFY `additional_amount` decimal(21,9) not null default 0, MODIFY `tax_on_flexible_benefit` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0
2025-02-18 16:19:18,668 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` ADD INDEX `salary_component_index`(`salary_component`), ADD INDEX `exempted_from_income_tax_index`(`exempted_from_income_tax`), ADD INDEX `is_tax_applicable_index`(`is_tax_applicable`), ADD INDEX `variable_based_on_taxable_salary_index`(`variable_based_on_taxable_salary`), ADD INDEX `creation`(`creation`)
2025-02-18 16:19:18,935 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:19,169 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Applicable Component` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:19,396 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Leave` MODIFY `available_leaves` decimal(21,9) not null default 0, MODIFY `used_leaves` decimal(21,9) not null default 0, MODIFY `total_allocated_leaves` decimal(21,9) not null default 0, MODIFY `expired_leaves` decimal(21,9) not null default 0, MODIFY `pending_leaves` decimal(21,9) not null default 0
2025-02-18 16:19:19,431 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Leave` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:19,726 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application` MODIFY `max_benefits` decimal(21,9) not null default 0, MODIFY `pro_rata_dispensed_amount` decimal(21,9) not null default 0, MODIFY `remaining_benefit` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0
2025-02-18 16:19:19,758 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:20,085 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `max_benefit_amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-02-18 16:19:20,130 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD INDEX `creation`(`creation`)
2025-02-18 16:19:20,397 WARNING database DDL Query made to DB:
create table `tabSalary Withholding` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`posting_date` date,
`payroll_frequency` varchar(140),
`number_of_withholding_cycles` int(11) not null default 0,
`status` varchar(140) default 'Draft',
`from_date` date,
`to_date` date,
`date_of_joining` date,
`relieving_date` date,
`reason_for_withholding_salary` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
