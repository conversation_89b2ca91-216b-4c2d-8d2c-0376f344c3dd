["tabDocType", "tabCustom", "tabScheduled", "tabStock", "tabSingles", "tabBank Transaction", "tabPatch", "tabJournal Entry Account", "tabSales", "tabTag", "tabVehicle Service", "tabReport", "tabFeedback Form", "tabContainer", "tabHas Role", "tabSales Invoice Item", "tabPurchase Receipt Item", "tabWeb Form List Column", "tabScheduled Job Type", "tabInstalled", "tabPurchase Invoice", "tabTag Link", "tabCompany", "tabJournal", "tabTask", "tabPrint Heading", "tabPurchase", "tabVehicle Log", "tabPayment Entry", "tabLeave <PERSON>", "tabComment", "tabRole", "tabStock Entry Detail", "tabDeleted", "tabLanded", "tabDocField", "tabBOM", "tabEmployee", "tabBank Account", "tabDocType State", "tabProperty Setter", "tabDashboard Chart Source", "tabDeleted Document", "tabAdvance Payment Ledger Entry", "tabEmployee OT Component", "tabHas", "tabPortal Menu Item", "tabPortal", "tabWeb", "tabBank", "tabDashboard Chart", "tabVehicle", "tabDocPerm", "tabAsset", "tabDocType Action", "tabProperty", "tabFeedback", "tabPurchase Invoice Item", "tabPrint", "tabBank Guarantee", "tabAdvance", "tabLanded Cost Voucher", "tabSalary", "tabDocType Link", "tabPatch Log", "tabLeave", "tabCustom Field", "tabDashboard Chart Field", "tabSupplier", "tabSalary Slip OT Component", "tabJournal Entry", "tabPayment Request", "tabPayment", "tabDashboard", "tabInstalled Application"]