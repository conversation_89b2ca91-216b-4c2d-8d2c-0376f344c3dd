2025-11-06 13:06:00,204 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,6) not null default 0, MODIFY `rate` decimal(21,4) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140), MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,3) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0
2025-11-06 14:40:31,459 WARNING database DDL Query made to DB:
ALTER TABLE `tabSteel Bar Production V2` MODIFY `hot_out_qty` decimal(21,9) not null default 0, MODIFY `rolling_mill_unit` decimal(21,9) not null default 0, MODIFY `miss_roll` decimal(21,9) not null default 0, MODIFY `total_amount_of_power_consumption` decimal(21,9) not null default 0, MODIFY `total_weight_in_mt` decimal(21,9) not null default 0, MODIFY `natural_gas_consmpt__per_ton` decimal(21,9) not null default 0, MODIFY `miss_roll_weight` decimal(21,9) not null default 0, MODIFY `power_cut_hrs` decimal(21,9), MODIFY `price_per_unit` decimal(21,9) not null default 0, MODIFY `roll_weight` decimal(21,9) not null default 0, MODIFY `tpc` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `total_weight_of_random` decimal(21,9) not null default 0, MODIFY `ng_consumption` decimal(21,9) not null default 0, MODIFY `r_s_ec` decimal(21,9) not null default 0, MODIFY `manufacturing_date` date, MODIFY `hot_out_weight` decimal(21,9) not null default 0, MODIFY `hot_out` decimal(21,9) not null default 0, MODIFY `total_running_hrs` decimal(21,9), MODIFY `prod_yield` decimal(21,9) not null default 0, MODIFY `scrap_steel` decimal(21,9) not null default 0, MODIFY `mech_bd_hrs` decimal(21,9), MODIFY `miss_roll_qty` decimal(21,9) not null default 0, MODIFY `internal_bd_hrs` decimal(21,9), MODIFY `rolling_mill_unit_per_ton` decimal(21,9) not null default 0, MODIFY `mill_yield` decimal(21,9) not null default 0, MODIFY `total_weight_of_raw_material_in_mt` decimal(21,9) not null default 0, MODIFY `total_bd_hrs` decimal(21,9)
