
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>VSD Fleet Management System - User Guide</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            page-break-before: always;
        }
        h1:first-of-type {
            page-break-before: avoid;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #95a5a6;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        h3 {
            color: #7f8c8d;
            margin-top: 20px;
        }
        code {
            background-color: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        pre {
            background-color: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
            overflow-x: auto;
        }
        pre code {
            background-color: transparent;
            padding: 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        ul, ol {
            margin: 10px 0;
            padding-left: 30px;
        }
        li {
            margin: 5px 0;
        }
        blockquote {
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin: 20px 0;
            color: #555;
            font-style: italic;
        }
        .page-break {
            page-break-after: always;
        }
        @media print {
            body {
                max-width: 100%;
            }
            h1, h2, h3 {
                page-break-after: avoid;
            }
        }
    </style>
</head>
<body>
<h1>VSD Fleet Management System - Comprehensive User Guide</h1>
<h2>Table of Contents</h2>
<ol>
<li><a href="#introduction">Introduction</a></li>
<li><a href="#what-is-vsd-fleet-ms">What is VSD Fleet MS?</a></li>
<li><a href="#installation">Installation</a></li>
<li><a href="#initial-setup--configuration">Initial Setup &amp; Configuration</a></li>
<li><a href="#master-data-setup">Master Data Setup</a></li>
<li><a href="#complete-workflow-guide">Complete Workflow Guide</a></li>
<li><a href="#reports--analytics">Reports &amp; Analytics</a></li>
<li><a href="#integration-with-clearing-app">Integration with Clearing App</a></li>
<li><a href="#best-practices">Best Practices</a></li>
</ol>
<hr />
<h2>Introduction</h2>
<p>The <strong>VSD Fleet Management System</strong> is a comprehensive Frappe/ERPNext application designed to streamline and optimize transport operations. It provides end-to-end management of fleet operations, from vehicle registration to trip completion and invoicing.</p>
<h3>Key Benefits</h3>
<ul>
<li><strong>Centralized Fleet Management</strong>: Manage all trucks, trailers, and drivers in one place</li>
<li><strong>Real-time Trip Tracking</strong>: Monitor trip status and locations in real-time</li>
<li><strong>Automated Invoicing</strong>: Generate customer invoices directly from transportation orders</li>
<li><strong>Fuel Management</strong>: Track fuel consumption and manage fuel requests</li>
<li><strong>Financial Integration</strong>: Seamless integration with ERPNext accounting</li>
<li><strong>Compliance Ready</strong>: Maintain vehicle inspections and regulatory compliance</li>
</ul>
<hr />
<h2>What is VSD Fleet MS?</h2>
<p>VSD Fleet MS is a complete transportation management solution that handles:</p>
<h3>Core Modules</h3>
<ol>
<li><strong>Master Data Management</strong></li>
<li>Trucks, Trailers, and Drivers</li>
<li>Routes and Locations</li>
<li>Cargo Types</li>
<li>
<p>Fixed Expenses</p>
</li>
<li>
<p><strong>Transportation Operations</strong></p>
</li>
<li>Transportation Orders</li>
<li>Cargo Registration</li>
<li>Manifest Preparation</li>
<li>Trip Management</li>
<li>
<p>Vehicle Inspections</p>
</li>
<li>
<p><strong>Financial Management</strong></p>
</li>
<li>Automated Invoicing</li>
<li>Expense Tracking</li>
<li>Fuel Management</li>
<li>
<p>Payment Requests</p>
</li>
<li>
<p><strong>Reporting &amp; Analytics</strong></p>
</li>
<li>Trip Reports</li>
<li>Fuel Expense Reports</li>
<li>Trip Report and Expenses</li>
</ol>
<hr />
<h2>Installation</h2>
<h3>Prerequisites</h3>
<ul>
<li>Frappe Framework installed</li>
<li>ERPNext installed (recommended)</li>
<li>Bench setup completed</li>
</ul>
<h3>Installation Steps</h3>
<ol>
<li>
<p><strong>Navigate to your Frappe Bench directory:</strong>
   <code>bash
   cd /path/to/frappe-bench</code></p>
</li>
<li>
<p><strong>Get the app from the repository:</strong>
   <code>bash
   bench get-app https://github.com/VVSD-LTD/vsd_fleet_ms.git</code></p>
</li>
<li>
<p><strong>Install the app on your site:</strong>
   <code>bash
   bench --site [your-site-name] install-app vsd_fleet_ms</code></p>
</li>
<li>
<p><strong>Migrate your site:</strong>
   <code>bash
   bench --site [your-site-name] migrate</code></p>
</li>
<li>
<p><strong>Restart bench:</strong>
   <code>bash
   bench restart</code></p>
</li>
</ol>
<hr />
<h2>Initial Setup &amp; Configuration</h2>
<h3>Step 1: Access Transport Settings</h3>
<p>Navigate to: <strong>Fleet MS &gt; Settings &gt; Transport Settings</strong></p>
<p>This is a <strong>Single DocType</strong> (only one record exists) that controls all fleet management configurations.</p>
<h3>Step 2: Configure Essential Settings</h3>
<h4>A. Warehouse Configuration</h4>
<ul>
<li><strong>Vehicle Fuel Parent Warehouse</strong>: Select the parent warehouse for fuel management</li>
<li>This should be a group warehouse</li>
<li>All truck-specific fuel warehouses will be created under this parent</li>
</ul>
<h4>B. Item Groups</h4>
<ul>
<li><strong>Sales Item Group</strong>: Item group for transportation services</li>
<li><strong>Fuel Item Group</strong>: Item group for fuel items</li>
<li><strong>Fuel Item</strong>: Default fuel item for stock management</li>
</ul>
<h4>C. Account Groups</h4>
<ul>
<li><strong>Expense Account Group</strong>: Define expense accounts for transportation costs</li>
<li>Add multiple expense account groups as needed</li>
<li>
<p>These will be available when creating fixed expenses</p>
</li>
<li>
<p><strong>Cash or Bank Account Group</strong>: Define cash/bank accounts for payments</p>
</li>
<li>Add multiple account groups for different payment methods</li>
</ul>
<h4>D. Accounting Dimensions</h4>
<p>Configure accounting dimensions to track financial data across different dimensions:</p>
<p><strong>Fields:</strong>
- <strong>Dimension Name</strong>: Select from existing accounting dimensions (e.g., Truck, Route, Driver)
- <strong>Source DocType</strong>: The doctype where the dimension value comes from (e.g., Transport Assignments)
- <strong>Source Type</strong>: Field, Value, or Child
- <strong>Source Field Name</strong>: Field name in the source doctype
- <strong>Target DocType</strong>: Where to apply the dimension (e.g., Sales Invoice)
- <strong>Target Type</strong>: Main or Child
- <strong>Target Field Name</strong>: Field name in the target doctype</p>
<p><strong>Example Configuration:</strong></p>
<div class="codehilite"><pre><span></span><code><span class="n">Dimension</span><span class="w"> </span><span class="nl">Name:</span><span class="w"> </span><span class="n">Truck</span>
<span class="n">Source</span><span class="w"> </span><span class="nl">DocType:</span><span class="w"> </span><span class="n">Transport</span><span class="w"> </span><span class="n">Assignments</span>
<span class="n">Source</span><span class="w"> </span><span class="nl">Type:</span><span class="w"> </span><span class="n">Field</span>
<span class="n">Source</span><span class="w"> </span><span class="n">Field</span><span class="w"> </span><span class="nl">Name:</span><span class="w"> </span><span class="n">assigned_vehicle</span>
<span class="n">Target</span><span class="w"> </span><span class="nl">DocType:</span><span class="w"> </span><span class="n">Sales</span><span class="w"> </span><span class="n">Invoice</span>
<span class="n">Target</span><span class="w"> </span><span class="nl">Type:</span><span class="w"> </span><span class="n">Main</span>
<span class="n">Target</span><span class="w"> </span><span class="n">Field</span><span class="w"> </span><span class="nl">Name:</span><span class="w"> </span><span class="n">truck</span>
</code></pre></div>

<hr />
<h2>Master Data Setup</h2>
<h3>1. Countries &amp; Locations</h3>
<p><strong>Purpose</strong>: Define countries and cities for route planning</p>
<p><strong>Navigation</strong>: Fleet MS &gt; Masters &gt; Countries</p>
<p><strong>Steps:</strong>
1. Create a new Country record
2. Enter country name
3. Add cities/locations within that country
4. Save</p>
<p><strong>Why</strong>: Required for defining cargo origins, destinations, and route planning</p>
<hr />
<h3>2. Transport Locations</h3>
<p><strong>Purpose</strong>: Define specific locations for trip tracking</p>
<p><strong>Navigation</strong>: Fleet MS &gt; Masters &gt; Transport Location</p>
<p><strong>Steps:</strong>
1. Create new location
2. Enter location name
3. Select location type (Border, City, Port, etc.)
4. Mark if it's a local or international border
5. Save</p>
<p><strong>Why</strong>: Used in route planning and trip tracking</p>
<hr />
<h3>3. Trip Routes</h3>
<p><strong>Purpose</strong>: Define standard routes with distances and fuel consumption</p>
<p><strong>Navigation</strong>: Fleet MS &gt; Masters &gt; Trip Routes</p>
<p><strong>Steps:</strong>
1. Create new route
2. Enter route name (e.g., "Dar es Salaam to Kampala")
3. Select starting point
4. Select ending point
5. Add route steps in the "Trip Steps" table:
   - Location
   - Distance (km)
   - Fuel consumption quantity
   - Location type
   - Mark if border crossing
6. System automatically calculates:
   - Total distance
   - Total fuel consumption
7. Save</p>
<p><strong>Why</strong>: Pre-defined routes ensure consistency and accurate fuel/cost estimation</p>
<hr />
<h3>4. Cargo Types</h3>
<p><strong>Purpose</strong>: Categorize different types of cargo</p>
<p><strong>Navigation</strong>: Fleet MS &gt; Masters &gt; Cargo Types</p>
<p><strong>Steps:</strong>
1. Create new cargo type
2. Enter cargo type name (e.g., "Container", "Loose Cargo", "Bulk")
3. Add description
4. Save</p>
<p><strong>Why</strong>: Helps in organizing and reporting on different cargo categories</p>
<hr />
<h3>5. Truck Registration</h3>
<p><strong>Purpose</strong>: Register and manage all trucks in the fleet</p>
<p><strong>Navigation</strong>: Fleet MS &gt; Masters &gt; Truck</p>
<p><strong>Steps:</strong>
1. Create new Truck
2. <strong>Overview Tab:</strong>
   - License Plate Number
   - Truck Number (unique identifier)
   - Make (e.g., Scania, Volvo)
   - Model
   - Manufacturing Year
   - Acquisition Year
   - Status (Available, On Trip, Under Maintenance, etc.)</p>
<ol>
<li><strong>Details Tab:</strong></li>
<li>Fuel Type (Diesel, Petrol)</li>
<li>Fuel UOM (Liters, Gallons)</li>
<li>Engine Number</li>
<li>Chassis Number</li>
<li>
<p>Odometer Value</p>
</li>
<li>
<p><strong>Transport Section:</strong></p>
</li>
<li>Maintain Stock: Check if you want to track fuel stock for this truck</li>
<li>Fuel Warehouse: Select warehouse (if maintain stock is checked)</li>
<li>Current Driver: Assign default driver</li>
<li>
<p>Current Trip: Auto-populated when truck is on trip</p>
</li>
<li>
<p><strong>Image</strong>: Upload truck photo</p>
</li>
<li>Save</li>
</ol>
<p><strong>Important Notes:</strong>
- Truck Number must be unique
- If "Maintain Stock" is checked, a fuel warehouse must be selected
- Truck status automatically updates when assigned to trips
- Disabled trucks won't appear in active selections</p>
<hr />
<h3>6. Trailer Registration</h3>
<p><strong>Purpose</strong>: Register trailers that can be attached to trucks</p>
<p><strong>Navigation</strong>: Fleet MS &gt; Masters &gt; Trailer</p>
<p><strong>Steps:</strong>
1. Create new Trailer
2. Enter trailer details:
   - Trailer Number (unique)
   - License Plate
   - Make and Model
   - Manufacturing Year
   - Trailer Type (Flatbed, Container, Tanker, etc.)
   - Status
3. Save</p>
<hr />
<h3>7. Driver Registration</h3>
<p><strong>Purpose</strong>: Register and manage all drivers</p>
<p><strong>Navigation</strong>: Fleet MS &gt; Masters &gt; Driver</p>
<p><strong>Steps:</strong>
1. Create new Truck Driver
2. Enter driver details:
   - Driver Name
   - License Number
   - License Categories (A, B, C, D, E)
   - License Expiry Date
   - Phone Number
   - Email
   - Status (Active, Inactive, On Leave)
3. Upload driver photo and license documents
4. Save</p>
<p><strong>Important</strong>: Only active drivers appear in trip assignment selections</p>
<hr />
<h3>8. Fixed Expenses</h3>
<p><strong>Purpose</strong>: Define recurring expenses for trips</p>
<p><strong>Navigation</strong>: Fleet MS &gt; Masters &gt; Fixed Expenses</p>
<p><strong>Steps:</strong>
1. Create new Fixed Expense
2. Enter description (e.g., "Border Crossing Fee", "Toll Charges")
3. Select currency
4. Enter fixed value
5. Select expense account (from configured account groups)
6. Select cash/bank account
7. Save</p>
<p><strong>Why</strong>: These expenses are automatically added to trips based on route configuration</p>
<hr />
<h2>Complete Workflow Guide</h2>
<h3>Workflow Overview</h3>
<div class="codehilite"><pre><span></span><code>Transportation Order → Cargo Registration → Manifest → Trip → Delivery → Invoice
</code></pre></div>

<h3>Phase 1: Transportation Order Creation</h3>
<p><strong>Purpose</strong>: Create a transportation request from a customer</p>
<p><strong>Navigation</strong>: Fleet MS &gt; Transactions &gt; Transportation Order</p>
<p><strong>Steps:</strong></p>
<ol>
<li><strong>Create New Transportation Order</strong></li>
<li>Click "New"</li>
<li>
<p>System auto-generates order number (TORD-YYYY-####)</p>
</li>
<li>
<p><strong>Order Details Section:</strong></p>
</li>
<li><strong>Date</strong>: Order date</li>
<li><strong>Customer</strong>: Select customer</li>
<li><strong>Transport Type</strong>: Cross Border or Internal</li>
<li>
<p><strong>Company</strong>: Your company</p>
</li>
<li>
<p><strong>Cargo Location &amp; Destination:</strong></p>
</li>
<li><strong>Cargo Location Country</strong>: Origin country</li>
<li><strong>Cargo Location City</strong>: Origin city</li>
<li><strong>Cargo Destination Country</strong>: Destination country</li>
<li>
<p><strong>Cargo Destination City</strong>: Destination city</p>
</li>
<li>
<p><strong>Consignee and Shipper:</strong></p>
</li>
<li><strong>Consignee</strong>: Final receiver of goods</li>
<li>
<p><strong>Shipper</strong>: Sender of goods</p>
</li>
<li>
<p><strong>Border &amp; Transportation Instructions:</strong></p>
</li>
<li><strong>Border 1/2/3 Clearing Agent</strong>: If crossing borders</li>
<li>
<p><strong>Special Instructions</strong>: Any special handling requirements</p>
</li>
<li>
<p><strong>Cargo Information:</strong></p>
</li>
<li><strong>Cargo Type</strong>: Container or Loose Cargo</li>
<li><strong>Goods Description</strong>: What is being transported</li>
<li><strong>Cargo Description</strong>: Detailed description</li>
<li><strong>Amount</strong>: Quantity</li>
<li>
<p><strong>Unit</strong>: Unit of measure</p>
</li>
<li>
<p><strong>References (if applicable):</strong></p>
</li>
<li><strong>Reference DocType</strong>: Link to source document (e.g., Clearing File)</li>
<li><strong>Reference DocName</strong>: Source document name</li>
<li>
<p><strong>File Number</strong>: Related file number</p>
</li>
<li>
<p><strong>Save</strong> the Transportation Order</p>
</li>
</ol>
<p><strong>What Happens Next:</strong>
- Order is created in draft status
- Ready for cargo assignment
- Can be linked to from other modules (like Clearing)</p>
<hr />
<h3>Phase 2: Cargo Assignment</h3>
<p><strong>Purpose</strong>: Assign specific cargo details and allocate transport resources</p>
<p><strong>From Transportation Order:</strong></p>
<ol>
<li><strong>Assign Transport Table:</strong></li>
<li>Click "Add Row" in the Assign Transport child table</li>
<li><strong>Cargo</strong>: Link to cargo registration (if exists)</li>
<li><strong>Container Number</strong>: For containerized cargo</li>
<li><strong>Amount</strong>: Quantity to transport</li>
<li><strong>Units</strong>: Unit of measure</li>
<li><strong>Expected Loading Date</strong>: When cargo will be loaded</li>
<li><strong>Route</strong>: Select pre-defined route</li>
<li>
<p><strong>Transporter Type</strong>:</p>
<ul>
<li>In House: Using your own trucks</li>
<li>Sub-Contractor: Using external transporter</li>
</ul>
</li>
<li>
<p><strong>For In-House Transport:</strong></p>
</li>
<li><strong>Assigned Vehicle</strong>: Select truck</li>
<li><strong>Assigned Trailer</strong>: Select trailer (if needed)</li>
<li><strong>Assigned Driver</strong>: Select driver</li>
<li><strong>Vehicle Plate Number</strong>: Auto-filled</li>
<li><strong>Trailer Plate Number</strong>: Auto-filled</li>
<li>
<p><strong>Driver Name</strong>: Auto-filled</p>
</li>
<li>
<p><strong>For Sub-Contractor:</strong></p>
</li>
<li><strong>Sub Contractor</strong>: Select supplier</li>
<li><strong>Vehicle Plate Number</strong>: Enter manually</li>
<li><strong>Trailer Plate Number</strong>: Enter manually</li>
<li><strong>Driver Name</strong>: Enter manually</li>
<li>
<p><strong>Passport Number</strong>: Driver's passport</p>
</li>
<li>
<p><strong>Pricing:</strong></p>
</li>
<li><strong>Currency</strong>: Select currency</li>
<li><strong>Rate</strong>: Transportation rate</li>
<li>
<p><strong>Amount</strong>: Auto-calculated (quantity × rate)</p>
</li>
<li>
<p><strong>Save</strong> the Transportation Order</p>
</li>
</ol>
<p><strong>Important Notes:</strong>
- You can assign multiple cargo items in one order
- Each row can have different vehicles/routes
- System validates vehicle availability
- Assigned vehicles must not be "On Trip"</p>
<hr />
<h3>Phase 3: Cargo Registration (Alternative Entry Point)</h3>
<p><strong>Purpose</strong>: Register cargo independently before creating transportation orders</p>
<p><strong>Navigation</strong>: Fleet MS &gt; Transactions &gt; Cargo Registration</p>
<p><strong>Steps:</strong></p>
<ol>
<li><strong>Create New Cargo Registration</strong></li>
<li><strong>Customer</strong>: Select customer</li>
<li><strong>Company</strong>: Your company</li>
<li>
<p><strong>Posting Date</strong>: Registration date</p>
</li>
<li>
<p><strong>Cargo Details Table:</strong></p>
</li>
<li><strong>Cargo Type</strong>: Container/Loose Cargo</li>
<li><strong>Container Number</strong>: For containers</li>
<li><strong>Seal Number</strong>: Container seal</li>
<li><strong>Container Size</strong>: 20ft, 40ft, etc.</li>
<li><strong>Number of Packages</strong>: Quantity</li>
<li><strong>Weight</strong>: Cargo weight</li>
<li><strong>Bill UOM</strong>: Billing unit</li>
<li><strong>Net Weight (Tonne)</strong>: Weight in tonnes</li>
<li><strong>BL Number</strong>: Bill of Lading number</li>
<li><strong>Cargo Route</strong>: Select route</li>
<li><strong>Expected Loading Date</strong>: Loading date</li>
<li><strong>Expected Offloading Date</strong>: Delivery date</li>
<li><strong>Service Item</strong>: Billable service item</li>
<li><strong>Rate</strong>: Service rate</li>
<li>
<p><strong>Currency</strong>: Billing currency</p>
</li>
<li>
<p><strong>Cargo Location:</strong></p>
</li>
<li><strong>Cargo Location Country</strong>: Origin</li>
<li><strong>Cargo Loading City</strong>: Loading point</li>
<li><strong>Cargo Destination Country</strong>: Destination</li>
<li>
<p><strong>Cargo Destination City</strong>: Delivery point</p>
</li>
<li>
<p><strong>Transporter Assignment:</strong></p>
</li>
<li><strong>Transporter Type</strong>: In House/Sub-Contractor</li>
<li><strong>Assigned Vehicle</strong>: Select truck</li>
<li><strong>Assigned Trailer</strong>: Select trailer</li>
<li>
<p><strong>Assigned Driver</strong>: Select driver</p>
</li>
<li>
<p><strong>Save</strong> the Cargo Registration</p>
</li>
</ol>
<p><strong>What Happens:</strong>
- Cargo is registered in the system
- Can be linked to manifests
- Can be invoiced separately
- Tracks cargo status through the journey</p>
<hr />
<h3>Phase 4: Manifest Preparation</h3>
<p><strong>Purpose</strong>: Group multiple cargo items for a single trip</p>
<p><strong>Navigation</strong>: Fleet MS &gt; Trips &gt; Manifest</p>
<p><strong>Steps:</strong></p>
<ol>
<li><strong>Create New Manifest</strong></li>
<li>System auto-generates manifest number (MNFS-####)</li>
<li>
<p><strong>Posting Date</strong>: Manifest date</p>
</li>
<li>
<p><strong>Transporter Details:</strong></p>
</li>
<li><strong>Transporter Type</strong>: In House/Sub-Contractor</li>
<li>
<p><strong>Sub Contractor Name</strong>: If using sub-contractor</p>
</li>
<li>
<p><strong>Vehicle Details:</strong></p>
</li>
<li><strong>Truck</strong>: Select truck</li>
<li><strong>Truck License Plate No</strong>: Auto-filled</li>
<li><strong>Route</strong>: Select route</li>
<li>
<p><strong>Route Starting Point</strong>: Auto-filled from route</p>
</li>
<li>
<p><strong>Driver Details:</strong></p>
</li>
<li><strong>Assigned Driver</strong>: Select driver</li>
<li><strong>Driver Name</strong>: Auto-filled</li>
<li>
<p><strong>License Number</strong>: Auto-filled</p>
</li>
<li>
<p><strong>Trailer Configuration:</strong></p>
</li>
<li><strong>Has Trailers</strong>: Check if using trailers</li>
<li><strong>Trailer 1</strong>: Select first trailer</li>
<li><strong>Trailer 1 Type</strong>: Auto-filled</li>
<li>
<p><strong>Trailer 2</strong>: Select second trailer (if applicable)</p>
</li>
<li>
<p><strong>Add Cargo to Manifest:</strong></p>
</li>
<li>
<p><strong>Manifest Cargo Details Table:</strong></p>
<ul>
<li><strong>Cargo ID</strong>: Link to cargo registration</li>
<li><strong>Cargo Type</strong>: Container/Loose</li>
<li><strong>Container Number</strong>: Container ID</li>
<li><strong>Seal Number</strong>: Container seal</li>
<li><strong>Number of Packages</strong>: Quantity</li>
<li><strong>Weight</strong>: Total weight</li>
<li><strong>BL Number</strong>: Bill of Lading</li>
<li><strong>Customer Name</strong>: Cargo owner</li>
<li><strong>Expected Loading Date</strong>: Loading date</li>
<li><strong>Expected Offloading Date</strong>: Delivery date</li>
</ul>
</li>
<li>
<p><strong>Save</strong> the Manifest</p>
</li>
<li>
<p><strong>Submit</strong> the Manifest when ready</p>
</li>
</ol>
<p><strong>What Happens on Submit:</strong>
- If In-House transport:
  - Truck status changes to "On Trip"
  - Truck's current trip is updated
- Manifest is locked for editing
- Ready to create trip</p>
<hr />
<h3>Phase 5: Trip Creation &amp; Management</h3>
<p><strong>Purpose</strong>: Execute and track the actual transportation trip</p>
<p><strong>Navigation</strong>: Fleet MS &gt; Trips &gt; Trips</p>
<p><strong>Two Ways to Create a Trip:</strong></p>
<h4>Method 1: From Manifest</h4>
<ol>
<li>Open the Manifest</li>
<li>Click "Create Vehicle Trip" button</li>
<li>System creates trip with all manifest details pre-filled</li>
</ol>
<h4>Method 2: Manual Creation</h4>
<ol>
<li>Create new Trip</li>
<li>Fill in all details manually</li>
</ol>
<p><strong>Trip Details:</strong></p>
<ol>
<li><strong>Trip Information:</strong></li>
<li><strong>Naming Series</strong>: Auto-generated (TRIP-###)</li>
<li><strong>Manifest</strong>: Link to manifest</li>
<li><strong>Transporter Type</strong>: In House/Sub-Contractor</li>
<li><strong>Date</strong>: Trip start date</li>
<li><strong>Company</strong>: Your company</li>
<li><strong>Route</strong>: Select route</li>
<li>
<p><strong>Round Trip</strong>: Check if it's a round trip</p>
</li>
<li>
<p><strong>Vehicle &amp; Driver:</strong></p>
</li>
<li><strong>Truck Number</strong>: Select truck</li>
<li><strong>Truck License Plate</strong>: Auto-filled</li>
<li><strong>Assigned Driver</strong>: Select driver</li>
<li><strong>Driver Name</strong>: Auto-filled</li>
<li><strong>Phone Number</strong>: Driver contact</li>
<li>
<p><strong>License Number</strong>: Auto-filled</p>
</li>
<li>
<p><strong>Trailers:</strong></p>
</li>
<li><strong>Trailer 1</strong>: Select trailer</li>
<li>
<p><strong>Trailer 2</strong>: If applicable</p>
</li>
<li>
<p><strong>Trip Planning:</strong></p>
</li>
<li><strong>ETA End of Trip</strong>: Expected arrival</li>
<li>
<p><strong>Route Steps</strong>: Auto-populated from route</p>
<ul>
<li>Each step shows location, distance, fuel consumption</li>
</ul>
</li>
<li>
<p><strong>For In-House Trips:</strong></p>
</li>
<li>
<p><strong>Fuel Management:</strong></p>
<ul>
<li><strong>Total Fuel</strong>: Calculated from route</li>
<li><strong>Fuel Stock Out</strong>: Fuel issued</li>
<li><strong>Stock Out Entry</strong>: Link to stock entry</li>
</ul>
</li>
<li>
<p><strong>Expenses:</strong></p>
<ul>
<li>System auto-adds fixed expenses based on route</li>
<li><strong>Requested Fund Accounts Table</strong>: Shows all expenses</li>
<li><strong>Requested Fund Details</strong>: Breakdown of costs</li>
</ul>
</li>
<li>
<p><strong>Trip Status:</strong></p>
</li>
<li><strong>Trip Status</strong>: Pending, In Progress, Completed, Cancelled</li>
<li>
<p><strong>Location Update</strong>: Track current location</p>
</li>
<li>
<p><strong>Save</strong> the Trip</p>
</li>
<li>
<p><strong>Submit</strong> when trip starts</p>
</li>
</ol>
<p><strong>During the Trip:</strong>
- Update trip status as it progresses
- Add location updates
- Track fuel consumption
- Record any incidents or delays</p>
<p><strong>Trip Completion:</strong>
1. Update status to "Completed"
2. Record actual fuel consumption
3. Record any additional expenses
4. Submit final trip report</p>
<hr />
<h3>Phase 6: Vehicle Inspection</h3>
<p><strong>Purpose</strong>: Ensure vehicle roadworthiness before and after trips</p>
<p><strong>Navigation</strong>: Fleet MS &gt; Transactions &gt; Truck and Trailer Inspection</p>
<p><strong>When to Inspect:</strong>
- Before trip starts
- After trip completes
- Regular scheduled inspections
- After maintenance</p>
<p><strong>Steps:</strong></p>
<ol>
<li><strong>Create from Trip</strong> (Recommended):</li>
<li>Open the Trip</li>
<li>Click "Make Vehicle Inspection"</li>
<li>
<p>System pre-fills vehicle and driver details</p>
</li>
<li>
<p><strong>Or Create Manually:</strong></p>
</li>
<li>Create new Vehicle Inspection</li>
<li>Select vehicle</li>
<li>
<p>Select driver</p>
</li>
<li>
<p><strong>Inspection Sections:</strong></p>
</li>
<li><strong>Truck Brakes</strong>: Check all brake components</li>
<li><strong>Truck Air System</strong>: Air pressure, leaks</li>
<li><strong>Truck Coupling Devices</strong>: Fifth wheel, kingpin</li>
<li><strong>Truck Exhaust System</strong>: Emissions, leaks</li>
<li><strong>Truck Fuel System</strong>: Tanks, lines, leaks</li>
<li><strong>Truck Lighting</strong>: All lights functional</li>
<li><strong>Truck Safe Loading</strong>: Load security</li>
<li><strong>Truck Steering</strong>: Steering mechanism</li>
<li><strong>Truck Suspension</strong>: Springs, shocks</li>
<li><strong>Truck Tires</strong>: Tread depth, pressure</li>
<li><strong>Truck Wheels</strong>: Rims, lug nuts</li>
<li><strong>Truck Windows</strong>: Windshield, mirrors</li>
<li><strong>Truck Wipers</strong>: Functionality</li>
<li>
<p><strong>Emergency Equipment</strong>: Fire extinguisher, triangle</p>
</li>
<li>
<p><strong>For Each Section:</strong></p>
</li>
<li>Mark as Satisfactory/Unsatisfactory</li>
<li>
<p>Add remarks if needed</p>
</li>
<li>
<p><strong>Inspector Details:</strong></p>
</li>
<li>Inspector name</li>
<li>Inspection date</li>
<li>
<p>Signature</p>
</li>
<li>
<p><strong>Save and Submit</strong></p>
</li>
</ol>
<p><strong>What Happens:</strong>
- If unsatisfactory items found, vehicle may be flagged for maintenance
- Inspection record is maintained for compliance
- Can be printed for regulatory requirements</p>
<hr />
<h3>Phase 7: Invoicing</h3>
<p><strong>Purpose</strong>: Bill customers for transportation services</p>
<p><strong>Two Approaches:</strong></p>
<h4>Approach 1: From Transportation Order</h4>
<ol>
<li>Open Transportation Order</li>
<li>Select cargo rows to invoice (checkbox selection)</li>
<li>Click "Create Invoice" button</li>
<li>System creates Sales Invoice with:</li>
<li>Customer details</li>
<li>Service items from cargo</li>
<li>Quantities and rates</li>
<li>Accounting dimensions (if configured)</li>
<li>Taxes (auto-calculated)</li>
</ol>
<h4>Approach 2: From Cargo Registration</h4>
<ol>
<li>Open Cargo Registration</li>
<li>Select cargo rows to invoice</li>
<li>Click "Create Invoice" button</li>
<li>System creates Sales Invoice</li>
</ol>
<p><strong>Invoice Details:</strong>
- <strong>Customer</strong>: From order/cargo
- <strong>Posting Date</strong>: Current date
- <strong>Currency</strong>: From cargo/order
- <strong>Items</strong>: Transportation services
  - Item Code: Service item
  - Quantity: From cargo
  - Rate: From cargo
  - Amount: Auto-calculated
- <strong>Taxes</strong>: Auto-applied based on customer
- <strong>Accounting Dimensions</strong>: Auto-filled (Truck, Route, etc.)</p>
<p><strong>After Invoice Creation:</strong>
- Invoice reference is saved back to cargo/order
- Can track which cargo has been invoiced
- Submit invoice to post to accounts</p>
<hr />
<h3>Phase 8: Fuel Management</h3>
<p><strong>Purpose</strong>: Track and manage fuel consumption and requests</p>
<h4>Fuel Requests</h4>
<p><strong>Navigation</strong>: Fleet MS &gt; Transactions &gt; Fuel Requests</p>
<p><strong>Steps:</strong></p>
<ol>
<li><strong>Create New Fuel Request</strong></li>
<li><strong>Truck</strong>: Select truck</li>
<li><strong>Driver</strong>: Select driver</li>
<li><strong>Route</strong>: Select route</li>
<li>
<p><strong>Trip Reference</strong>: Link to trip (if applicable)</p>
</li>
<li>
<p><strong>Fuel Details:</strong></p>
</li>
<li><strong>Fuel Type</strong>: Diesel/Petrol</li>
<li><strong>Quantity Requested</strong>: Liters/Gallons</li>
<li><strong>UOM</strong>: Unit of measure</li>
<li><strong>Estimated Cost</strong>: Expected cost</li>
<li>
<p><strong>Fuel Station</strong>: Where to refuel</p>
</li>
<li>
<p><strong>Approval:</strong></p>
</li>
<li><strong>Request Status</strong>: Pending/Approved/Rejected</li>
<li><strong>Approved By</strong>: Approver name</li>
<li>
<p><strong>Approved Quantity</strong>: May differ from requested</p>
</li>
<li>
<p><strong>Save and Submit</strong></p>
</li>
</ol>
<p><strong>What Happens:</strong>
- Request is logged
- Can be approved/rejected
- Tracks fuel consumption per truck/trip
- Integrates with fuel expense reports</p>
<h4>Fuel Stock Management (If Maintain Stock is Enabled)</h4>
<p><strong>For Trucks with Fuel Warehouses:</strong></p>
<ol>
<li><strong>Stock Out (Fuel Issue):</strong></li>
<li>When trip starts, create Stock Entry</li>
<li><strong>Purpose</strong>: Material Issue</li>
<li><strong>From Warehouse</strong>: Truck's fuel warehouse</li>
<li><strong>Item</strong>: Fuel item</li>
<li><strong>Quantity</strong>: Fuel issued for trip</li>
<li>
<p>Link to Trip</p>
</li>
<li>
<p><strong>Stock In (Fuel Refill):</strong></p>
</li>
<li>When refueling, create Stock Entry</li>
<li><strong>Purpose</strong>: Material Receipt</li>
<li><strong>To Warehouse</strong>: Truck's fuel warehouse</li>
<li><strong>Item</strong>: Fuel item</li>
<li><strong>Quantity</strong>: Fuel added</li>
<li>
<p><strong>Rate</strong>: Fuel cost</p>
</li>
<li>
<p><strong>Fuel Balance:</strong></p>
</li>
<li>Check truck's fuel warehouse stock balance</li>
<li>Tracks fuel consumption per truck</li>
<li>Helps in fuel theft detection</li>
</ol>
<hr />
<h2>Reports &amp; Analytics</h2>
<h3>1. Trips Report</h3>
<p><strong>Purpose</strong>: Overview of all trips with key metrics</p>
<p><strong>Navigation</strong>: Fleet MS &gt; Reports &gt; Trips Report</p>
<p><strong>Filters:</strong>
- Date Range
- Truck
- Driver
- Route
- Trip Status
- Transporter Type</p>
<p><strong>Columns:</strong>
- Trip ID
- Date
- Truck
- Driver
- Route
- Status
- Distance
- Fuel Consumed
- Customer
- Invoice</p>
<p><strong>Use Cases:</strong>
- Monitor trip completion rates
- Analyze driver performance
- Track vehicle utilization
- Identify delays</p>
<hr />
<h3>2. Fuel Expense by Trip</h3>
<p><strong>Purpose</strong>: Analyze fuel consumption and costs per trip</p>
<p><strong>Navigation</strong>: Fleet MS &gt; Reports &gt; Fuel Expense by Trip</p>
<p><strong>Filters:</strong>
- Date Range
- Truck
- Route
- Trip</p>
<p><strong>Columns:</strong>
- Trip ID
- Truck
- Route
- Total Distance
- Fuel Consumed
- Fuel Cost
- Cost per KM
- Variance from Standard</p>
<p><strong>Use Cases:</strong>
- Identify fuel inefficiencies
- Compare actual vs. planned consumption
- Detect fuel theft
- Optimize routes for fuel efficiency</p>
<hr />
<h3>3. Trip Report and Expenses</h3>
<p><strong>Purpose</strong>: Comprehensive trip analysis with all expenses</p>
<p><strong>Navigation</strong>: Fleet MS &gt; Reports &gt; Trip Report and Expenses</p>
<p><strong>Filters:</strong>
- Date Range
- Truck
- Driver
- Customer</p>
<p><strong>Columns:</strong>
- Trip Details
- Revenue (Invoice Amount)
- Fuel Expenses
- Fixed Expenses
- Other Expenses
- Total Expenses
- Profit/Loss
- Profit Margin %</p>
<p><strong>Use Cases:</strong>
- Profitability analysis per trip
- Cost control
- Pricing decisions
- Performance evaluation</p>
<hr />
<h2>Integration with Clearing App</h2>
<h3>Overview</h3>
<p>The VSD Fleet MS app integrates seamlessly with the Clearing app to provide end-to-end logistics management from customs clearance to final delivery.</p>
<h3>Integration Points</h3>
<h4>1. Clearing File → Transportation Order</h4>
<p><strong>Scenario</strong>: Customer has imported goods that need customs clearance and transportation</p>
<p><strong>Workflow:</strong></p>
<ol>
<li><strong>Create Clearing File</strong> (in Clearing app)</li>
<li>Customer: Importer</li>
<li>Mode of Transport: Sea/Air/Road</li>
<li>Cargo Details: Containers, weight, etc.</li>
<li>Arrival Date: ETA</li>
<li>
<p>Destination: Final delivery location</p>
</li>
<li>
<p><strong>Automatic Transportation Order Creation:</strong></p>
</li>
<li>When clearing file status changes to specific status</li>
<li>Or manually create from Clearing File</li>
<li>
<p>System creates Transportation Order with:</p>
<ul>
<li>Reference DocType: "Clearing File"</li>
<li>Reference DocName: Clearing file number</li>
<li>Customer: From clearing file</li>
<li>Cargo Location: Port/Airport (from clearing file)</li>
<li>Cargo Destination: From clearing file</li>
<li>File Number: Clearing file number</li>
</ul>
</li>
<li>
<p><strong>Link Maintained:</strong></p>
</li>
<li>Transportation Order references Clearing File</li>
<li>Can navigate between documents</li>
<li>Status updates can be synchronized</li>
</ol>
<p><strong>Code Reference:</strong></p>
<div class="codehilite"><pre><span></span><code><span class="c1"># From clearing file, create transport order</span>
<span class="n">frappe</span><span class="o">.</span><span class="n">call</span><span class="p">({</span>
    <span class="n">method</span><span class="p">:</span> <span class="s2">&quot;vsd_fleet_ms.vsd_fleet_ms.doctype.transportation_order.transportation_order.create_transport_order&quot;</span><span class="p">,</span>
    <span class="n">args</span><span class="p">:</span> <span class="p">{</span>
        <span class="n">reference_doctype</span><span class="p">:</span> <span class="s2">&quot;Clearing File&quot;</span><span class="p">,</span>
        <span class="n">reference_docname</span><span class="p">:</span> <span class="n">clearing_file</span><span class="o">.</span><span class="n">name</span><span class="p">,</span>
        <span class="n">file_number</span><span class="p">:</span> <span class="n">clearing_file</span><span class="o">.</span><span class="n">name</span><span class="p">,</span>
        <span class="n">customer</span><span class="p">:</span> <span class="n">clearing_file</span><span class="o">.</span><span class="n">customer</span><span class="p">,</span>
        <span class="n">cargo_location_country</span><span class="p">:</span> <span class="n">clearing_file</span><span class="o">.</span><span class="n">cargo_country_of_origin</span><span class="p">,</span>
        <span class="n">cargo_destination_country</span><span class="p">:</span> <span class="s2">&quot;Tanzania&quot;</span><span class="p">,</span> <span class="o">//</span> <span class="ow">or</span> <span class="kn">from</span><span class="w"> </span><span class="nn">clearing</span> <span class="n">file</span>
        <span class="n">transport_type</span><span class="p">:</span> <span class="s2">&quot;Cross Border&quot;</span> <span class="o">//</span> <span class="ow">or</span> <span class="s2">&quot;Internal&quot;</span>
    <span class="p">}</span>
<span class="p">})</span>
</code></pre></div>

<h4>2. Container Tracking Integration</h4>
<p><strong>Scenario</strong>: Track containers from port clearance through delivery</p>
<p><strong>Workflow:</strong></p>
<ol>
<li><strong>Clearing File has Container Details:</strong></li>
<li>Container numbers</li>
<li>Seal numbers</li>
<li>Container sizes</li>
<li>
<p>BL numbers</p>
</li>
<li>
<p><strong>Create Cargo Registration:</strong></p>
</li>
<li>Link to Clearing File</li>
<li>Import container details</li>
<li>Container Number: From clearing</li>
<li>Seal Number: From clearing</li>
<li>
<p>BL Number: From clearing</p>
</li>
<li>
<p><strong>Manifest Creation:</strong></p>
</li>
<li>Add containers from cargo registration</li>
<li>System tracks container movement</li>
<li>
<p>Updates clearing file when delivered</p>
</li>
<li>
<p><strong>Status Synchronization:</strong></p>
</li>
<li>When trip status = "Completed"</li>
<li>Update clearing file status</li>
<li>Mark containers as "Delivered"</li>
</ol>
<h4>3. Document Flow</h4>
<p><strong>Complete Integrated Workflow:</strong></p>
<div class="codehilite"><pre><span></span><code>Clearing File (Clearing App)
    ↓
Transportation Order (Fleet MS)
    ↓
Cargo Registration (Fleet MS)
    ↓
Manifest (Fleet MS)
    ↓
Trip (Fleet MS)
    ↓
Delivery Confirmation
    ↓
Update Clearing File Status (Clearing App)
    ↓
Invoice Customer (Both Apps)
</code></pre></div>

<h4>4. Shared Data Elements</h4>
<p><strong>Common Fields Between Apps:</strong></p>
<table>
<thead>
<tr>
<th>Field</th>
<th>Clearing File</th>
<th>Transportation Order</th>
<th>Cargo Registration</th>
</tr>
</thead>
<tbody>
<tr>
<td>Customer</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
</tr>
<tr>
<td>Container Number</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
</tr>
<tr>
<td>BL Number</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
</tr>
<tr>
<td>Seal Number</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
</tr>
<tr>
<td>Origin Country</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
</tr>
<tr>
<td>Destination</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
</tr>
<tr>
<td>Cargo Description</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
</tr>
<tr>
<td>Weight</td>
<td>✓</td>
<td>-</td>
<td>✓</td>
</tr>
<tr>
<td>Arrival Date</td>
<td>✓</td>
<td>-</td>
<td>✓</td>
</tr>
</tbody>
</table>
<h4>5. Implementation Steps for Integration</h4>
<p><strong>Step 1: Configure Transport Settings</strong>
1. Go to Transport Settings
2. Configure accounting dimensions
3. Set up expense accounts
4. Configure fuel warehouses</p>
<p><strong>Step 2: Create Custom Fields (if needed)</strong></p>
<div class="codehilite"><pre><span></span><code><span class="c1">// Add clearing_file link field to Transportation Order</span>
<span class="p">{</span>
<span class="w">    </span><span class="nx">fieldname</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;clearing_file&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">fieldtype</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;Link&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">options</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;Clearing File&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">label</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;Clearing File&quot;</span>
<span class="p">}</span>
</code></pre></div>

<p><strong>Step 3: Create Server Script for Auto-Creation</strong></p>
<div class="codehilite"><pre><span></span><code><span class="c1"># Server Script: Clearing File - After Save</span>
<span class="k">if</span> <span class="n">doc</span><span class="o">.</span><span class="n">status</span> <span class="o">==</span> <span class="s2">&quot;Cleared&quot;</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">doc</span><span class="o">.</span><span class="n">transport_reference</span><span class="p">:</span>
    <span class="n">transport_order</span> <span class="o">=</span> <span class="n">frappe</span><span class="o">.</span><span class="n">get_doc</span><span class="p">({</span>
        <span class="s2">&quot;doctype&quot;</span><span class="p">:</span> <span class="s2">&quot;Transportation Order&quot;</span><span class="p">,</span>
        <span class="s2">&quot;reference_doctype&quot;</span><span class="p">:</span> <span class="s2">&quot;Clearing File&quot;</span><span class="p">,</span>
        <span class="s2">&quot;reference_docname&quot;</span><span class="p">:</span> <span class="n">doc</span><span class="o">.</span><span class="n">name</span><span class="p">,</span>
        <span class="s2">&quot;customer&quot;</span><span class="p">:</span> <span class="n">doc</span><span class="o">.</span><span class="n">customer</span><span class="p">,</span>
        <span class="s2">&quot;file_number&quot;</span><span class="p">:</span> <span class="n">doc</span><span class="o">.</span><span class="n">name</span><span class="p">,</span>
        <span class="s2">&quot;cargo_location_country&quot;</span><span class="p">:</span> <span class="n">doc</span><span class="o">.</span><span class="n">cargo_country_of_origin</span><span class="p">,</span>
        <span class="s2">&quot;cargo_destination_country&quot;</span><span class="p">:</span> <span class="s2">&quot;Tanzania&quot;</span><span class="p">,</span>
        <span class="s2">&quot;transport_type&quot;</span><span class="p">:</span> <span class="s2">&quot;Internal&quot;</span>
    <span class="p">})</span>
    <span class="n">transport_order</span><span class="o">.</span><span class="n">insert</span><span class="p">()</span>
    <span class="n">doc</span><span class="o">.</span><span class="n">db_set</span><span class="p">(</span><span class="s2">&quot;transport_reference&quot;</span><span class="p">,</span> <span class="n">transport_order</span><span class="o">.</span><span class="n">name</span><span class="p">)</span>
</code></pre></div>

<p><strong>Step 4: Create Button in Clearing File</strong></p>
<div class="codehilite"><pre><span></span><code><span class="c1">// Clearing File JS</span>
<span class="nx">frappe</span><span class="p">.</span><span class="nx">ui</span><span class="p">.</span><span class="nx">form</span><span class="p">.</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;Clearing File&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">refresh</span><span class="o">:</span><span class="w"> </span><span class="kd">function</span><span class="p">(</span><span class="nx">frm</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">frm</span><span class="p">.</span><span class="nx">doc</span><span class="p">.</span><span class="nx">status</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="s2">&quot;Cleared&quot;</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="o">!</span><span class="nx">frm</span><span class="p">.</span><span class="nx">doc</span><span class="p">.</span><span class="nx">transport_reference</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nx">frm</span><span class="p">.</span><span class="nx">add_custom_button</span><span class="p">(</span><span class="nx">__</span><span class="p">(</span><span class="s1">&#39;Create Transport Order&#39;</span><span class="p">),</span><span class="w"> </span><span class="kd">function</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="nx">frappe</span><span class="p">.</span><span class="nx">call</span><span class="p">({</span>
<span class="w">                    </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;vsd_fleet_ms.vsd_fleet_ms.doctype.transportation_order.transportation_order.create_transport_order&quot;</span><span class="p">,</span>
<span class="w">                    </span><span class="nx">args</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">                        </span><span class="nx">reference_doctype</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;Clearing File&quot;</span><span class="p">,</span>
<span class="w">                        </span><span class="nx">reference_docname</span><span class="o">:</span><span class="w"> </span><span class="nx">frm</span><span class="p">.</span><span class="nx">doc</span><span class="p">.</span><span class="nx">name</span><span class="p">,</span>
<span class="w">                        </span><span class="nx">customer</span><span class="o">:</span><span class="w"> </span><span class="nx">frm</span><span class="p">.</span><span class="nx">doc</span><span class="p">.</span><span class="nx">customer</span><span class="p">,</span>
<span class="w">                        </span><span class="nx">file_number</span><span class="o">:</span><span class="w"> </span><span class="nx">frm</span><span class="p">.</span><span class="nx">doc</span><span class="p">.</span><span class="nx">name</span>
<span class="w">                    </span><span class="p">},</span>
<span class="w">                    </span><span class="nx">callback</span><span class="o">:</span><span class="w"> </span><span class="kd">function</span><span class="p">(</span><span class="nx">r</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">r</span><span class="p">.</span><span class="nx">message</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                            </span><span class="nx">frappe</span><span class="p">.</span><span class="nx">set_route</span><span class="p">(</span><span class="s1">&#39;Form&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;Transportation Order&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">r</span><span class="p">.</span><span class="nx">message</span><span class="p">);</span>
<span class="w">                        </span><span class="p">}</span>
<span class="w">                    </span><span class="p">}</span>
<span class="w">                </span><span class="p">});</span>
<span class="w">            </span><span class="p">});</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">});</span>
</code></pre></div>

<h4>6. Best Practices for Integration</h4>
<ol>
<li><strong>Use Reference Fields:</strong></li>
<li>Always link documents using reference_doctype and reference_docname</li>
<li>Maintains data integrity</li>
<li>
<p>Enables easy navigation</p>
</li>
<li>
<p><strong>Status Synchronization:</strong></p>
</li>
<li>Update clearing file status when trip is completed</li>
<li>Use document events (on_submit, on_update)</li>
<li>
<p>Avoid circular updates</p>
</li>
<li>
<p><strong>Data Validation:</strong></p>
</li>
<li>Validate that clearing file exists before creating transport order</li>
<li>Check that containers are not already assigned</li>
<li>
<p>Verify customer matches</p>
</li>
<li>
<p><strong>Permissions:</strong></p>
</li>
<li>Ensure users have access to both apps</li>
<li>Configure role permissions appropriately</li>
<li>
<p>Use ignore_permissions=True in automated scripts</p>
</li>
<li>
<p><strong>Error Handling:</strong></p>
</li>
<li>Wrap API calls in try-except blocks</li>
<li>Log errors for debugging</li>
<li>Show user-friendly error messages</li>
</ol>
<hr />
<h2>Best Practices</h2>
<h3>1. Master Data Management</h3>
<p><strong>Do's:</strong>
- ✓ Keep truck and driver information up-to-date
- ✓ Regularly update vehicle status
- ✓ Maintain accurate odometer readings
- ✓ Set up routes before creating trips
- ✓ Define all fixed expenses upfront</p>
<p><strong>Don'ts:</strong>
- ✗ Don't delete trucks/drivers with historical data
- ✗ Don't skip vehicle inspections
- ✗ Don't assign vehicles that are "On Trip"
- ✗ Don't modify routes that have active trips</p>
<h3>2. Trip Management</h3>
<p><strong>Do's:</strong>
- ✓ Create manifests before trips
- ✓ Submit trips when they actually start
- ✓ Update trip status regularly
- ✓ Record actual fuel consumption
- ✓ Complete vehicle inspections</p>
<p><strong>Don'ts:</strong>
- ✗ Don't submit trips without fuel stock out (for in-house)
- ✗ Don't skip manifest creation
- ✗ Don't forget to update trip status
- ✗ Don't leave trips in "Pending" status indefinitely</p>
<h3>3. Financial Management</h3>
<p><strong>Do's:</strong>
- ✓ Invoice customers promptly after delivery
- ✓ Track all expenses per trip
- ✓ Reconcile fuel consumption regularly
- ✓ Use accounting dimensions for better reporting
- ✓ Review trip profitability reports</p>
<p><strong>Don'ts:</strong>
- ✗ Don't create invoices without submitting trips
- ✗ Don't skip expense recording
- ✗ Don't ignore fuel variances
- ✗ Don't forget to configure accounting dimensions</p>
<h3>4. Fuel Management</h3>
<p><strong>Do's:</strong>
- ✓ Enable "Maintain Stock" for better fuel tracking
- ✓ Create fuel warehouses per truck
- ✓ Record all fuel refills
- ✓ Compare actual vs. planned consumption
- ✓ Investigate significant variances</p>
<p><strong>Don'ts:</strong>
- ✗ Don't skip fuel stock entries
- ✗ Don't ignore negative fuel balances
- ✗ Don't forget to update fuel prices
- ✗ Don't mix fuel types in same warehouse</p>
<h3>5. Reporting &amp; Analytics</h3>
<p><strong>Do's:</strong>
- ✓ Run reports regularly
- ✓ Analyze trip profitability
- ✓ Monitor vehicle utilization
- ✓ Track driver performance
- ✓ Use filters to focus on specific periods/vehicles</p>
<p><strong>Don'ts:</strong>
- ✗ Don't ignore report insights
- ✗ Don't skip variance analysis
- ✗ Don't forget to export reports for management
- ✗ Don't overlook trends in data</p>
<h3>6. Integration with Clearing</h3>
<p><strong>Do's:</strong>
- ✓ Link clearing files to transport orders
- ✓ Synchronize container information
- ✓ Update statuses in both systems
- ✓ Use consistent customer records
- ✓ Maintain document references</p>
<p><strong>Don'ts:</strong>
- ✗ Don't create duplicate records
- ✗ Don't break document links
- ✗ Don't skip status updates
- ✗ Don't ignore integration errors</p>
<hr />
<h2>Troubleshooting</h2>
<h3>Common Issues and Solutions</h3>
<h4>Issue 1: Cannot Assign Vehicle to Trip</h4>
<p><strong>Symptom</strong>: Error "Vehicle is in trip"
<strong>Solution</strong>:
- Check vehicle status
- Complete or cancel existing trip
- Update vehicle status to "Available"</p>
<h4>Issue 2: Fuel Stock Entry Not Created</h4>
<p><strong>Symptom</strong>: Trip submitted but no stock entry
<strong>Solution</strong>:
- Check if "Maintain Stock" is enabled for truck
- Verify fuel warehouse is set
- Check fuel item in Transport Settings
- Ensure sufficient fuel stock</p>
<h4>Issue 3: Invoice Not Creating</h4>
<p><strong>Symptom</strong>: Create Invoice button doesn't work
<strong>Solution</strong>:
- Save the document first
- Select cargo rows to invoice
- Check if rows already invoiced
- Verify customer has default currency</p>
<h4>Issue 4: Route Steps Not Showing</h4>
<p><strong>Symptom</strong>: Route selected but steps not populated
<strong>Solution</strong>:
- Check if route has trip steps defined
- Verify route is saved properly
- Refresh the form
- Re-select the route</p>
<h4>Issue 5: Accounting Dimensions Not Applied</h4>
<p><strong>Symptom</strong>: Dimensions not showing in invoice
<strong>Solution</strong>:
- Check Transport Settings configuration
- Verify dimension mapping is correct
- Ensure source fields have values
- Check target doctype has dimension fields</p>
<hr />
<h2>Conclusion</h2>
<p>The VSD Fleet Management System provides a comprehensive solution for managing transportation operations. By following this guide:</p>
<ol>
<li><strong>Setup</strong>: Configure settings and master data properly</li>
<li><strong>Operations</strong>: Follow the workflow from order to delivery</li>
<li><strong>Integration</strong>: Leverage integration with Clearing app for complete logistics management</li>
<li><strong>Monitoring</strong>: Use reports to track performance and profitability</li>
<li><strong>Best Practices</strong>: Follow recommended practices for optimal results</li>
</ol>
<h3>Getting Help</h3>
<ul>
<li><strong>Documentation</strong>: Refer to this guide</li>
<li><strong>GitHub</strong>: https://github.com/VVSD-LTD/vsd_fleet_ms</li>
<li><strong>Support</strong>: <EMAIL></li>
<li><strong>Community</strong>: Frappe Forum</li>
</ul>
<h3>Next Steps</h3>
<ol>
<li>Complete initial setup and configuration</li>
<li>Import master data (trucks, drivers, routes)</li>
<li>Create test transportation order</li>
<li>Run through complete workflow</li>
<li>Generate and review reports</li>
<li>Configure integration with Clearing app (if applicable)</li>
<li>Train users on the system</li>
<li>Go live!</li>
</ol>
<hr />
<p><strong>Document Version</strong>: 1.0
<strong>Last Updated</strong>: 2025-01-04
<strong>Author</strong>: VV SYSTEMS DEVELOPER LTD
<strong>License</strong>: MIT</p>
</body>
</html>
